import helmet from 'helmet';
import rateLimit from 'express-rate-limit';
import slowDown from 'express-slow-down';
import { body, param, query, validationResult } from 'express-validator';
import DOMPurify from 'isomorphic-dompurify';

// ============================================================================
// RATE LIMITING - Protección contra ataques DDoS
// ============================================================================

// Rate limiting general por IP
export const generalLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutos
  max: 100, // 100 requests por IP por ventana
  message: {
    error: 'Demasiadas solicitudes desde esta IP, intente más tarde',
    retryAfter: '15 minutos'
  },
  standardHeaders: true, // Incluir headers rate limit info
  legacyHeaders: false,
  handler: (req, res) => {
    console.warn(`Rate limit exceeded for IP: ${req.ip}, Path: ${req.path}`);
    res.status(429).json({
      error: 'Demasiadas solicitudes',
      retryAfter: Math.round(req.rateLimit.resetTime / 1000)
    });
  }
});

// Rate limiting estricto para autenticación
export const authLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutos
  max: 5, // Solo 5 intentos de login por IP
  skipSuccessfulRequests: true, // No contar requests exitosos
  message: {
    error: 'Demasiados intentos de autenticación, intente más tarde',
    retryAfter: '15 minutos'
  },
  handler: (req, res) => {
    console.warn(`Auth rate limit exceeded for IP: ${req.ip}`);
    res.status(429).json({
      error: 'Demasiados intentos de autenticación',
      retryAfter: Math.round(req.rateLimit.resetTime / 1000)
    });
  }
});

// Rate limiting para búsquedas y catálogo
export const searchLimiter = rateLimit({
  windowMs: 1 * 60 * 1000, // 1 minuto
  max: 30, // 30 búsquedas por minuto
  message: {
    error: 'Demasiadas búsquedas, intente más tarde',
    retryAfter: '1 minuto'
  }
});

// Slow down para requests intensivos
export const searchSlowDown = slowDown({
  windowMs: 15 * 60 * 1000, // 15 minutos
  delayAfter: 10, // Después de 10 requests
  delayMs: 500, // Delay de 500ms
  maxDelayMs: 5000 // Máximo 5 segundos de delay
});

// ============================================================================
// HEADERS DE SEGURIDAD - Protección contra ataques comunes
// ============================================================================

export const securityHeaders = helmet({
  // Content Security Policy
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: [
        "'self'", 
        "'unsafe-inline'", // Necesario para Tailwind CSS
        "https://fonts.googleapis.com"
      ],
      fontSrc: [
        "'self'", 
        "https://fonts.gstatic.com"
      ],
      imgSrc: [
        "'self'", 
        "data:", 
        "https://*.supabase.co",
        "https://cr-seg-ind.pages.dev",
        "https://cr-work.up.railway.app"
      ],
      scriptSrc: [
        "'self'",
        "'unsafe-eval'" // Necesario para Vite en desarrollo
      ],
      connectSrc: [
        "'self'", 
        "https://*.supabase.co",
        "https://cr-work.up.railway.app",
        "wss://*.supabase.co" // WebSocket para realtime
      ],
      frameSrc: ["'none'"],
      objectSrc: ["'none'"],
      upgradeInsecureRequests: []
    }
  },
  
  // HTTP Strict Transport Security
  hsts: {
    maxAge: 31536000, // 1 año
    includeSubDomains: true,
    preload: true
  },
  
  // Prevenir clickjacking
  frameguard: { action: 'deny' },
  
  // Prevenir MIME type sniffing
  noSniff: true,
  
  // Referrer Policy
  referrerPolicy: { policy: 'strict-origin-when-cross-origin' },
  
  // Permissions Policy
  permissionsPolicy: {
    features: {
      camera: [],
      microphone: [],
      geolocation: [],
      payment: []
    }
  }
});

// ============================================================================
// VALIDACIÓN Y SANITIZACIÓN DE INPUTS
// ============================================================================

// Middleware para manejar errores de validación
export const handleValidationErrors = (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    console.warn('Validation errors:', {
      ip: req.ip,
      path: req.path,
      errors: errors.array()
    });
    
    return res.status(400).json({
      error: 'Datos de entrada inválidos',
      details: errors.array().map(err => ({
        field: err.path,
        message: err.msg
      }))
    });
  }
  next();
};

// Validaciones para productos
export const validateProduct = [
  body('name')
    .isLength({ min: 1, max: 200 })
    .trim()
    .escape()
    .withMessage('Nombre debe tener entre 1 y 200 caracteres'),
  
  body('description')
    .optional()
    .isLength({ max: 2000 })
    .trim()
    .withMessage('Descripción no puede exceder 2000 caracteres'),
  
  body('price')
    .isFloat({ min: 0, max: 999999 })
    .withMessage('Precio debe ser un número positivo válido'),
  
  body('categories')
    .isArray({ min: 1, max: 10 })
    .withMessage('Debe tener entre 1 y 10 categorías'),
  
  body('categories.*')
    .isString()
    .isLength({ min: 1, max: 50 })
    .trim()
    .escape(),
  
  body('industries')
    .optional()
    .isArray({ max: 10 })
    .withMessage('Máximo 10 industrias'),
  
  body('brand')
    .optional()
    .isLength({ max: 100 })
    .trim()
    .escape(),
  
  handleValidationErrors
];

// Validaciones para búsqueda
export const validateSearch = [
  query('search')
    .optional()
    .isLength({ max: 100 })
    .trim()
    .escape()
    .withMessage('Término de búsqueda muy largo'),
  
  query('category')
    .optional()
    .isString()
    .isLength({ max: 50 })
    .trim()
    .escape(),
  
  query('page')
    .optional()
    .isInt({ min: 1, max: 1000 })
    .withMessage('Página debe ser un número entre 1 y 1000'),
  
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Límite debe ser un número entre 1 y 100'),
  
  handleValidationErrors
];

// Validaciones para autenticación
export const validateAuth = [
  body('email')
    .isEmail()
    .normalizeEmail()
    .isLength({ max: 255 })
    .withMessage('Email inválido'),
  
  body('password')
    .isLength({ min: 8, max: 128 })
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
    .withMessage('Password debe tener al menos 8 caracteres, una mayúscula, una minúscula y un número'),
  
  handleValidationErrors
];

// ============================================================================
// SANITIZACIÓN DE CONTENIDO HTML
// ============================================================================

export const sanitizeHtml = (req, res, next) => {
  // Campos que pueden contener HTML
  const htmlFields = ['description', 'caracteristicas', 'especificaciones'];
  
  htmlFields.forEach(field => {
    if (req.body[field] && typeof req.body[field] === 'string') {
      req.body[field] = DOMPurify.sanitize(req.body[field], {
        ALLOWED_TAGS: ['p', 'br', 'strong', 'em', 'ul', 'ol', 'li'],
        ALLOWED_ATTR: []
      });
    }
  });
  
  next();
};

// ============================================================================
// MIDDLEWARE DE LOGGING DE SEGURIDAD
// ============================================================================

export const securityLogger = (req, res, next) => {
  // Log de requests sospechosos
  const suspiciousPatterns = [
    /script/i,
    /javascript/i,
    /vbscript/i,
    /onload/i,
    /onerror/i,
    /<.*>/,
    /union.*select/i,
    /drop.*table/i
  ];
  
  const requestData = JSON.stringify({
    url: req.url,
    body: req.body,
    query: req.query,
    headers: req.headers
  });
  
  const isSuspicious = suspiciousPatterns.some(pattern => 
    pattern.test(requestData)
  );
  
  if (isSuspicious) {
    console.warn('Suspicious request detected:', {
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      url: req.url,
      method: req.method,
      timestamp: new Date().toISOString()
    });
  }
  
  next();
};

// ============================================================================
// MIDDLEWARE COMBINADO DE SEGURIDAD
// ============================================================================

export const applySecurity = (app) => {
  // Headers de seguridad
  app.use(securityHeaders);
  
  // Rate limiting general
  app.use(generalLimiter);
  
  // Logging de seguridad
  app.use(securityLogger);
  
  // Rate limiting específico para rutas
  app.use('/api/auth', authLimiter);
  app.use('/api/search', searchLimiter, searchSlowDown);
  app.use('/api/catalog', searchLimiter);
  
  console.log('✅ Security middleware applied successfully');
};
