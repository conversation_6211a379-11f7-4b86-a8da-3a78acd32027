import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { ArrowRight } from 'lucide-react';

// ============================================================================
// HERO OPTIMIZADO PARA LCP < 1.5s
// ============================================================================

const HeroOptimized = () => {
  const [imageLoaded, setImageLoaded] = useState(false);
  const [showContent, setShowContent] = useState(false);

  // Mostrar contenido inmediatamente, no esperar imagen
  useEffect(() => {
    // Mostrar contenido crítico inmediatamente
    const timer = setTimeout(() => {
      setShowContent(true);
    }, 50); // Mínimo delay para evitar flash

    return () => clearTimeout(timer);
  }, []);

  // Precargar imagen de fondo de forma optimizada
  useEffect(() => {
    const img = new Image();
    
    // Usar imagen optimizada y responsive
    const heroImageUrl = window.innerWidth <= 768 
      ? 'https://ik.imagekit.io/crwork/hero-bg-mobile.webp?tr=w-768,h-1024,q-80,f-webp'
      : 'https://ik.imagekit.io/crwork/hero-bg.webp?tr=w-1920,h-1080,q-85,f-webp';
    
    img.onload = () => {
      setImageLoaded(true);
    };
    
    img.onerror = () => {
      // Fallback a imagen estática si falla la optimizada
      setImageLoaded(true);
    };
    
    img.src = heroImageUrl;
  }, []);

  return (
    <section className="relative min-h-screen flex items-center overflow-hidden">
      {/* Background optimizado con lazy loading */}
      <div className="absolute inset-0">
        {/* Placeholder color mientras carga la imagen */}
        <div className="absolute inset-0 bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900" />
        
        {/* Imagen de fondo optimizada */}
        <div 
          className={`absolute inset-0 bg-cover bg-center transition-opacity duration-700 ${
            imageLoaded ? 'opacity-100' : 'opacity-0'
          }`}
          style={{
            backgroundImage: imageLoaded 
              ? `url(${window.innerWidth <= 768 
                  ? 'https://ik.imagekit.io/crwork/hero-bg-mobile.webp?tr=w-768,h-1024,q-80,f-webp'
                  : 'https://ik.imagekit.io/crwork/hero-bg.webp?tr=w-1920,h-1080,q-85,f-webp'
                })`
              : 'none',
            willChange: 'opacity'
          }}
        />
        
        {/* Overlay optimizado */}
        <div className="absolute inset-0 bg-black/40" />
        <div className="absolute inset-0 bg-gradient-to-r from-black/50 via-transparent to-black/30" />
        <div className="absolute bottom-0 left-0 right-0 h-32 bg-gradient-to-t from-black/60 to-transparent" />
      </div>

      {/* Contenido crítico - se muestra inmediatamente */}
      <div className={`container mx-auto px-4 sm:px-6 lg:px-8 relative z-10 transition-all duration-500 ${
        showContent ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4'
      }`}>
        <div className="max-w-4xl">
          {/* Título principal - Critical content */}
          <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-white leading-tight mb-6">
            Seguridad Industrial y{' '}
            <br />
            <span className="bg-gradient-to-r from-amber-400 to-amber-600 bg-clip-text text-transparent">
              Equipos de Protección
            </span>
          </h1>

          {/* Subtítulo optimizado */}
          <p className="text-xl md:text-2xl text-gray-200 mb-8 max-w-2xl leading-relaxed font-light">
            Protegemos lo más valioso de tu empresa: tu gente. 
            Equipos de seguridad industrial de primera calidad.
          </p>

          {/* CTAs optimizados */}
          <div className="flex flex-col sm:flex-row gap-4">
            {/* CTA Primario optimizado */}
            <Link
              to="/catalog"
              className="inline-flex items-center justify-center 
                         px-8 py-4 text-lg font-semibold rounded-xl
                         text-white bg-gradient-to-r from-amber-500 to-amber-600
                         hover:from-amber-600 hover:to-amber-700
                         transform transition-all duration-300
                         hover:scale-105 hover:shadow-xl hover:shadow-amber-500/25
                         focus:outline-none focus:ring-2 focus:ring-amber-500 focus:ring-offset-2 focus:ring-offset-black
                         sm:min-w-[200px]"
              aria-label="Ver catálogo de productos de seguridad industrial"
            >
              Ver Catálogo
              <ArrowRight className="ml-2 w-5 h-5" />
            </Link>

            {/* CTA Secundario */}
            <Link
              to="/nosotros"
              className="inline-flex items-center justify-center 
                         px-8 py-4 text-lg font-semibold rounded-xl
                         text-white border-2 border-white/30 backdrop-blur-sm
                         hover:bg-white/10 hover:border-white/50
                         transition-all duration-300
                         focus:outline-none focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-black
                         sm:min-w-[200px]"
              aria-label="Conocer más sobre CR Work"
            >
              Conocer Más
            </Link>
          </div>

          {/* Indicadores de confianza - Above the fold */}
          <div className="mt-12 flex flex-wrap items-center gap-6 text-white/80">
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse" />
              <span className="text-sm font-medium">Productos Certificados</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-blue-400 rounded-full animate-pulse" />
              <span className="text-sm font-medium">Envío a Todo el País</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-amber-400 rounded-full animate-pulse" />
              <span className="text-sm font-medium">Asesoramiento Técnico</span>
            </div>
          </div>
        </div>
      </div>

      {/* Scroll indicator optimizado */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 z-10">
        <div className="animate-bounce">
          <div className="w-6 h-10 border-2 border-white/50 rounded-full flex justify-center">
            <div className="w-1 h-3 bg-white/70 rounded-full mt-2 animate-pulse" />
          </div>
        </div>
      </div>

      {/* Schema.org structured data para SEO */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "WebPage",
            "name": "CR Work - Seguridad Industrial",
            "description": "Equipos de protección personal y seguridad industrial de primera calidad",
            "url": "https://cr-seg-ind.pages.dev",
            "mainEntity": {
              "@type": "Organization",
              "name": "CR Work",
              "description": "Proveedor líder de equipos de protección personal"
            }
          })
        }}
      />
    </section>
  );
};

export default HeroOptimized;
