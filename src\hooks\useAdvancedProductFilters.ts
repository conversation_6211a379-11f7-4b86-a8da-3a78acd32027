import { useState, useEffect, useMemo, useCallback } from 'react';
import { useSearchParams } from 'react-router-dom';
import { supabase } from '../lib/supabase';
import { 
  CatalogProduct, 
  AdvancedProductFilters, 
  ProductSearchResult, 
  FilterFacet,
  ProductPropertyType 
} from '../types/catalog';

interface UseAdvancedProductFiltersReturn {
  // Estado de filtros
  filters: AdvancedProductFilters;
  setFilters: (filters: AdvancedProductFilters) => void;
  updateFilter: (key: keyof AdvancedProductFilters, value: any) => void;
  clearFilters: () => void;
  
  // Productos y resultados
  products: ProductSearchResult[];
  isLoading: boolean;
  error: string | null;
  totalCount: number;
  
  // Facetas dinámicas
  facets: FilterFacet[];
  propertyTypes: ProductPropertyType[];
  
  // Funciones de utilidad
  searchProducts: (searchTerm: string) => void;
  addTag: (tag: string) => void;
  removeTag: (tag: string) => void;
  setProperty: (property: string, value: any) => void;
  removeProperty: (property: string) => void;
}

export const useAdvancedProductFilters = (): UseAdvancedProductFiltersReturn => {
  const [searchParams, setSearchParams] = useSearchParams();
  
  // Estado de filtros
  const [filters, setFiltersState] = useState<AdvancedProductFilters>({
    searchTerm: searchParams.get('search') || '',
    categories: searchParams.getAll('category'),
    industries: searchParams.getAll('industry'),
    brands: searchParams.getAll('brand'),
    tags: searchParams.getAll('tag'),
    properties: {},
    inStock: searchParams.get('inStock') === 'true'
  });
  
  // Estado de datos
  const [products, setProducts] = useState<ProductSearchResult[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [totalCount, setTotalCount] = useState(0);
  const [propertyTypes, setPropertyTypes] = useState<ProductPropertyType[]>([]);

  // Sincronizar filtros con URL
  useEffect(() => {
    const newSearchParams = new URLSearchParams();
    
    if (filters.searchTerm) newSearchParams.set('search', filters.searchTerm);
    filters.categories?.forEach(cat => newSearchParams.append('category', cat));
    filters.industries?.forEach(ind => newSearchParams.append('industry', ind));
    filters.brands?.forEach(brand => newSearchParams.append('brand', brand));
    filters.tags?.forEach(tag => newSearchParams.append('tag', tag));
    if (filters.inStock) newSearchParams.set('inStock', 'true');
    
    // Agregar propiedades como parámetros
    Object.entries(filters.properties || {}).forEach(([key, value]) => {
      if (Array.isArray(value)) {
        value.forEach(v => newSearchParams.append(`prop_${key}`, String(v)));
      } else {
        newSearchParams.set(`prop_${key}`, String(value));
      }
    });
    
    setSearchParams(newSearchParams);
  }, [filters, setSearchParams]);

  // Cargar tipos de propiedades
  useEffect(() => {
    const loadPropertyTypes = async () => {
      try {
        const { data, error } = await supabase
          .from('product_property_types')
          .select('*')
          .eq('is_filterable', true)
          .order('sort_order', { ascending: true });
        
        if (error) throw error;
        setPropertyTypes(data || []);
      } catch (err) {
        console.error('Error loading property types:', err);
      }
    };
    
    loadPropertyTypes();
  }, []);

  // Función para buscar productos
  const searchProducts = useCallback(async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      // Usar la función de búsqueda avanzada de PostgreSQL
      const { data, error } = await supabase.rpc('search_products_advanced', {
        search_term: filters.searchTerm || '',
        category_filters: filters.categories || [],
        industry_filters: filters.industries || [],
        tag_filters: filters.tags || [],
        property_filters: filters.properties || {}
      });
      
      if (error) throw error;
      
      let results = data || [];
      
      // Aplicar filtros adicionales en el cliente si es necesario
      if (filters.brands && filters.brands.length > 0) {
        results = results.filter((product: any) => 
          filters.brands!.includes(product.brand?.toUpperCase())
        );
      }
      
      if (filters.inStock) {
        results = results.filter((product: any) => product.stock > 0);
      }
      
      if (filters.priceRange) {
        const [min, max] = filters.priceRange;
        results = results.filter((product: any) => 
          product.price >= min && product.price <= max
        );
      }
      
      setProducts(results);
      setTotalCount(results.length);
    } catch (err) {
      console.error('Error searching products:', err);
      setError(err instanceof Error ? err.message : 'Error desconocido');
    } finally {
      setIsLoading(false);
    }
  }, [filters]);

  // Ejecutar búsqueda cuando cambien los filtros
  useEffect(() => {
    searchProducts();
  }, [searchProducts]);

  // Generar facetas dinámicas
  const facets = useMemo<FilterFacet[]>(() => {
    if (!products.length) return [];
    
    const facetMap = new Map<string, Map<string, number>>();
    
    // Generar facetas para propiedades
    propertyTypes.forEach(propType => {
      const valueMap = new Map<string, number>();
      
      products.forEach(product => {
        const value = product.properties?.[propType.name];
        if (value !== undefined && value !== null) {
          if (Array.isArray(value)) {
            value.forEach(v => {
              const key = String(v);
              valueMap.set(key, (valueMap.get(key) || 0) + 1);
            });
          } else {
            const key = String(value);
            valueMap.set(key, (valueMap.get(key) || 0) + 1);
          }
        }
      });
      
      if (valueMap.size > 0) {
        facetMap.set(propType.name, valueMap);
      }
    });
    
    // Convertir a formato de facetas
    return Array.from(facetMap.entries()).map(([propName, valueMap]) => {
      const propType = propertyTypes.find(pt => pt.name === propName);
      return {
        name: propName,
        display_name: propType?.display_name || propName,
        type: propType?.data_type || 'text',
        values: Array.from(valueMap.entries())
          .map(([value, count]) => ({
            value: propType?.data_type === 'number' ? Number(value) : value,
            count,
            selected: false // TODO: implementar lógica de selección
          }))
          .sort((a, b) => b.count - a.count)
      } as FilterFacet;
    });
  }, [products, propertyTypes]);

  // Funciones de utilidad
  const setFilters = useCallback((newFilters: AdvancedProductFilters) => {
    setFiltersState(newFilters);
  }, []);

  const updateFilter = useCallback((key: keyof AdvancedProductFilters, value: any) => {
    setFiltersState(prev => ({ ...prev, [key]: value }));
  }, []);

  const clearFilters = useCallback(() => {
    setFiltersState({
      searchTerm: '',
      categories: [],
      industries: [],
      brands: [],
      tags: [],
      properties: {},
      inStock: false
    });
  }, []);

  const searchProductsWithTerm = useCallback((searchTerm: string) => {
    updateFilter('searchTerm', searchTerm);
  }, [updateFilter]);

  const addTag = useCallback((tag: string) => {
    setFiltersState(prev => ({
      ...prev,
      tags: [...(prev.tags || []), tag].filter((t, i, arr) => arr.indexOf(t) === i)
    }));
  }, []);

  const removeTag = useCallback((tag: string) => {
    setFiltersState(prev => ({
      ...prev,
      tags: (prev.tags || []).filter(t => t !== tag)
    }));
  }, []);

  const setProperty = useCallback((property: string, value: any) => {
    setFiltersState(prev => ({
      ...prev,
      properties: {
        ...prev.properties,
        [property]: value
      }
    }));
  }, []);

  const removeProperty = useCallback((property: string) => {
    setFiltersState(prev => {
      const newProperties = { ...prev.properties };
      delete newProperties[property];
      return {
        ...prev,
        properties: newProperties
      };
    });
  }, []);

  return {
    filters,
    setFilters,
    updateFilter,
    clearFilters,
    products,
    isLoading,
    error,
    totalCount,
    facets,
    propertyTypes,
    searchProducts: searchProductsWithTerm,
    addTag,
    removeTag,
    setProperty,
    removeProperty
  };
};
