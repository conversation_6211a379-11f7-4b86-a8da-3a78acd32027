import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Cargar variables de entorno
dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Faltan variables de entorno VITE_SUPABASE_URL o VITE_SUPABASE_ANON_KEY');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

// Productos de ejemplo que demuestran la estructura de 3 niveles EPP
const sampleEPPProducts = [
  // NIVEL 3: Protección Craneana - Cascos Dieléctricos
  {
    name: 'Casco Dieléctrico MSA V-Gard Clase E',
    description: 'Casco de seguridad dieléctrico para trabajos eléctricos de alta tensión',
    price: 89.99,
    categories: ['epp', 'craneana', 'dielectricos'], // 3 niveles
    industries: ['ELECTRICIDAD', 'CONSTRUCCION'],
    brand: 'MSA',
    stock: 25,
    image_url: 'cascos/msa-vgard-dielectrico.jpg',
    caracteristicas: 'Casco dieléctrico clase E, resistente hasta 20000V, material ABS de alta resistencia',
    especificaciones: 'Peso: 350g, Voltaje máximo: 20kV, Temperatura de uso: -30°C a +50°C',
    presentacion: 'Unidad individual con barbijo ajustable',
    documentacion: 'Certificado ANSI Z89.1 Clase E, Manual de usuario'
  },
  
  // NIVEL 3: Protección Craneana - Cascos de Altura
  {
    name: 'Casco de Altura 3M SecureFit con Barbijo',
    description: 'Casco especializado para trabajos en altura con sistema de suspensión',
    price: 75.50,
    categories: ['epp', 'craneana', 'altura'],
    industries: ['CONSTRUCCION', 'TORRES'],
    brand: '3M',
    stock: 18,
    image_url: 'cascos/3m-securefit-altura.jpg',
    caracteristicas: 'Sistema de suspensión para altura, barbijo de 4 puntos, ventilación superior',
    especificaciones: 'Peso: 380g, Material: HDPE, Barbijo: Nylon reforzado',
    presentacion: 'Casco con barbijo y manual de instalación',
    documentacion: 'Certificado EN 397, Instrucciones de uso en altura'
  },

  // NIVEL 3: Protección Ocular - Anteojos
  {
    name: 'Anteojos de Seguridad Uvex Sportstyle',
    description: 'Anteojos de protección con lentes de policarbonato y protección UV',
    price: 28.75,
    categories: ['epp', 'ocular', 'anteojos'],
    industries: ['LABORATORIO', 'CONSTRUCCION', 'MANUFACTURA'],
    brand: 'UVEX',
    stock: 45,
    image_url: 'proteccion-ocular/uvex-sportstyle.jpg',
    caracteristicas: 'Lentes de policarbonato, protección UV 99.9%, antiempañante',
    especificaciones: 'Peso: 25g, Material: Policarbonato, Protección: UV400',
    presentacion: 'Anteojos con estuche protector',
    documentacion: 'Certificado ANSI Z87.1, Guía de mantenimiento'
  },

  // NIVEL 3: Protección Facial - Soldador
  {
    name: 'Careta de Soldador 3M Speedglas 9100',
    description: 'Careta de soldadura con filtro auto-oscureciente y protección lateral',
    price: 245.00,
    categories: ['epp', 'facial', 'soldador'],
    industries: ['SOLDADURA', 'METALURGIA', 'CONSTRUCCION'],
    brand: '3M',
    stock: 12,
    image_url: 'proteccion-facial/3m-speedglas-9100.jpg',
    caracteristicas: 'Filtro auto-oscureciente DIN 8-13, protección lateral, peso balanceado',
    especificaciones: 'Tiempo de conmutación: 0.1ms, Batería: Litio, Peso: 520g',
    presentacion: 'Careta completa con filtros de repuesto',
    documentacion: 'Manual de soldadura, Certificado EN 175'
  },

  // NIVEL 3: Protección Respiratoria - Mascarillas N95
  {
    name: 'Mascarilla N95 3M 8210 Plus',
    description: 'Mascarilla descartable N95 con válvula de exhalación',
    price: 3.25,
    categories: ['epp', 'respiratoria', 'mascarillas-descartables'],
    industries: ['CONSTRUCCION', 'PINTURA', 'LIMPIEZA'],
    brand: '3M',
    stock: 200,
    image_url: 'respiratoria/3m-8210-plus.jpg',
    caracteristicas: 'Eficiencia 95%, válvula Cool Flow, ajuste cómodo',
    especificaciones: 'Filtración: 95% partículas 0.3μm, Resistencia respiratoria: <35Pa',
    presentacion: 'Caja de 20 unidades',
    documentacion: 'Certificado NIOSH N95, Instrucciones de uso'
  },

  // NIVEL 3: Protección de Manos - Anticorte
  {
    name: 'Guantes Anticorte HyFlex 11-541',
    description: 'Guantes de alta resistencia al corte con recubrimiento de nitrilo',
    price: 18.90,
    categories: ['epp', 'manos', 'anticorte'],
    industries: ['VIDRIO', 'METAL', 'CONSTRUCCION'],
    brand: 'ANSELL',
    stock: 35,
    image_url: 'guantes/hyflex-11-541.jpg',
    caracteristicas: 'Nivel de corte 5, fibra HPPE, recubrimiento nitrilo en palma',
    especificaciones: 'Tallas: 7-11, Grosor: 1.3mm, Resistencia corte: Nivel 5',
    presentacion: 'Par de guantes en bolsa individual',
    documentacion: 'Certificado EN 388, Tabla de tallas'
  },

  // NIVEL 3: Calzado - Botinas
  {
    name: 'Botinas de Seguridad Caterpillar Holton',
    description: 'Botinas de cuero con puntera de acero y plantilla antiperforation',
    price: 125.00,
    categories: ['epp', 'calzado', 'botinas'],
    industries: ['CONSTRUCCION', 'MINERIA', 'INDUSTRIA'],
    brand: 'CATERPILLAR',
    stock: 28,
    image_url: 'calzado/cat-holton-botinas.jpg',
    caracteristicas: 'Puntera de acero, plantilla antiperforation, cuero resistente',
    especificaciones: 'Tallas: 39-46, Peso: 1.8kg/par, Resistencia: 200J',
    presentacion: 'Par en caja individual con manual',
    documentacion: 'Certificado EN ISO 20345, Guía de cuidado'
  },

  // NIVEL 2: Solo tipo de protección (sin subcategoría específica)
  {
    name: 'Kit de Protección Auditiva Universal',
    description: 'Kit completo de protección auditiva para diferentes ambientes',
    price: 45.00,
    categories: ['epp', 'auditiva'], // Solo 2 niveles
    industries: ['INDUSTRIA', 'CONSTRUCCION', 'AEROPUERTO'],
    brand: 'HONEYWELL',
    stock: 22,
    image_url: 'auditiva/honeywell-kit-universal.jpg',
    caracteristicas: 'Incluye tapones y protectores de copa, reducción 25-35dB',
    especificaciones: 'NRR: 30dB, Material: Espuma + PVC, Ajustable',
    presentacion: 'Kit en estuche con accesorios',
    documentacion: 'Certificado ANSI S3.19, Manual de uso'
  }
];

async function generateEPPSampleData() {
  console.log('🚀 Generando datos de ejemplo EPP con estructura de 3 niveles...');

  try {
    let insertedCount = 0;
    let errorCount = 0;

    for (const product of sampleEPPProducts) {
      try {
        // Verificar si el producto ya existe
        const { data: existingProduct } = await supabase
          .from('products')
          .select('id')
          .eq('name', product.name)
          .single();

        if (existingProduct) {
          console.log(`⚠️  Producto ya existe: ${product.name}`);
          continue;
        }

        // Insertar el producto
        const { data, error } = await supabase
          .from('products')
          .insert([product])
          .select();

        if (error) {
          throw error;
        }

        insertedCount++;
        console.log(`✅ Insertado: ${product.name}`);
        console.log(`   📂 Niveles: ${product.categories.join(' → ')}`);
        console.log(`   🏭 Industrias: ${product.industries.join(', ')}`);
        console.log(`   💰 Precio: $${product.price}`);

      } catch (error) {
        errorCount++;
        console.error(`❌ Error insertando ${product.name}:`, error.message);
      }
    }

    console.log(`\n📊 Resumen de inserción:`);
    console.log(`   ✅ Productos insertados: ${insertedCount}`);
    console.log(`   ❌ Errores: ${errorCount}`);
    console.log(`   📦 Total procesados: ${sampleEPPProducts.length}`);

    // Mostrar estructura de niveles creada
    console.log(`\n🏗️  Estructura de niveles EPP creada:`);
    console.log(`   📁 Nivel 1 (EPP): Todos los productos`);
    console.log(`   📂 Nivel 2 (Tipos): craneana, ocular, facial, respiratoria, manos, calzado, auditiva`);
    console.log(`   📄 Nivel 3 (Específicos): dielectricos, altura, anteojos, soldador, mascarillas-descartables, anticorte, botinas`);

  } catch (error) {
    console.error('❌ Error general:', error);
    process.exit(1);
  }
}

// Ejecutar el script
generateEPPSampleData()
  .then(() => {
    console.log('\n🎉 ¡Generación de datos EPP completada!');
    console.log('💡 Ahora puedes ejecutar populateProductTagsAndProperties.js para asignar tags y propiedades');
    process.exit(0);
  })
  .catch((error) => {
    console.error('💥 Error fatal:', error);
    process.exit(1);
  });
