<!DOCTYPE html>
<html lang="es">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    
    <!-- Critical CSS Inline para LCP optimizado -->
    <style>
      /* Critical CSS - Solo estilos above-the-fold */
      *,::before,::after{box-sizing:border-box;border-width:0;border-style:solid;border-color:#e5e7eb}
      html{line-height:1.5;-webkit-text-size-adjust:100%;-moz-tab-size:4;tab-size:4;font-family:Inter,system-ui,sans-serif;font-feature-settings:normal}
      body{margin:0;line-height:inherit;font-family:Inter,system-ui,sans-serif}
      
      /* Hero critical styles */
      .hero-container{position:relative;min-height:100vh;display:flex;align-items:center;overflow:hidden}
      .hero-bg{position:absolute;inset:0;background:linear-gradient(135deg,#0f172a 0%,#1e293b 50%,#0f172a 100%)}
      .hero-content{position:relative;z-index:10;max-width:1280px;margin:0 auto;padding:0 1rem}
      .hero-title{font-size:2.25rem;font-weight:700;color:white;line-height:1.2;margin-bottom:1.5rem}
      .hero-subtitle{font-size:1.25rem;color:#d1d5db;margin-bottom:2rem;max-width:32rem;line-height:1.6}
      .hero-cta{display:inline-flex;align-items:center;justify-content:center;padding:1rem 2rem;font-size:1.125rem;font-weight:600;border-radius:0.75rem;color:white;background:linear-gradient(90deg,#f59e0b 0%,#d97706 100%);text-decoration:none;transition:all 0.3s ease}
      .hero-cta:hover{transform:scale(1.05);box-shadow:0 20px 25px -5px rgba(245,158,11,0.25)}
      
      /* Navbar critical */
      .navbar{position:fixed;top:0;left:0;right:0;z-index:50;background:rgba(15,23,42,0.95);backdrop-filter:blur(10px)}
      .navbar-content{max-width:1280px;margin:0 auto;padding:0 1rem;height:4rem;display:flex;align-items:center;justify-content:space-between}
      .logo{height:2.5rem;width:auto}
      
      /* Loading optimization */
      .loading-hidden{opacity:0;visibility:hidden}
      .loading-visible{opacity:1;visibility:visible;transition:opacity 0.3s ease}
      
      /* Font display optimization */
      @font-face{font-family:'Inter';font-style:normal;font-weight:400;font-display:swap;src:url('https://fonts.gstatic.com/s/inter/v12/UcCO3FwrK3iLTeHuS_fvQtMwCp50KnMw2boKoduKmMEVuLyfAZ9hiA.woff2') format('woff2')}
      @font-face{font-family:'Inter';font-style:normal;font-weight:600;font-display:swap;src:url('https://fonts.gstatic.com/s/inter/v12/UcCO3FwrK3iLTeHuS_fvQtMwCp50KnMw2boKoduKmMEVuGKYAZ9hiA.woff2') format('woff2')}
      @font-face{font-family:'Inter';font-style:normal;font-weight:700;font-display:swap;src:url('https://fonts.gstatic.com/s/inter/v12/UcCO3FwrK3iLTeHuS_fvQtMwCp50KnMw2boKoduKmMEVuFuYAZ9hiA.woff2') format('woff2')}
      
      /* Responsive optimizations */
      @media (min-width:768px){
        .hero-title{font-size:3rem}
        .hero-subtitle{font-size:1.5rem}
        .hero-content{padding:0 1.5rem}
      }
      @media (min-width:1024px){
        .hero-title{font-size:3.75rem}
        .hero-content{padding:0 2rem}
      }
    </style>
    
    <!-- SEO Meta Tags Optimizados -->
    <title>CR Work Seguridad Industrial | EPP Certificados | Envío Nacional</title>
    <meta name="description" content="🛡️ Equipos de Protección Personal certificados. 3M, MSA, Honeywell. ✅ Envío gratis +$50k ⚡ Entrega 24-48hs 📞 Asesoramiento técnico especializado" />
    <meta name="keywords" content="EPP, seguridad industrial, cascos, guantes, anteojos, 3M, MSA, Argentina, certificados" />
    
    <!-- Open Graph optimizado -->
    <meta property="og:title" content="CR Work - Equipos de Protección Personal Certificados" />
    <meta property="og:description" content="Protegemos lo más valioso: tu gente. EPP certificados de las mejores marcas con envío nacional." />
    <meta property="og:image" content="https://cr-seg-ind.pages.dev/images/og-hero.webp" />
    <meta property="og:url" content="https://cr-seg-ind.pages.dev" />
    <meta property="og:type" content="website" />
    <meta property="og:site_name" content="CR Work Seguridad Industrial" />
    
    <!-- Twitter Card -->
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:title" content="CR Work - EPP Certificados" />
    <meta name="twitter:description" content="Equipos de Protección Personal de las mejores marcas. Envío nacional." />
    <meta name="twitter:image" content="https://cr-seg-ind.pages.dev/images/og-hero.webp" />
    
    <!-- Favicon optimizado -->
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
    <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
    <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />
    <link rel="manifest" href="/site.webmanifest" />
    
    <!-- Preloads críticos para LCP -->
    <link rel="preload" as="image" href="https://ik.imagekit.io/crwork/hero-bg.webp?tr=w-1920,h-1080,q-85,f-webp" media="(min-width: 769px)" />
    <link rel="preload" as="image" href="https://ik.imagekit.io/crwork/hero-bg-mobile.webp?tr=w-768,h-1024,q-80,f-webp" media="(max-width: 768px)" />
    <link rel="preload" as="image" href="/images/cr-work-logo.webp" />
    
    <!-- Preload critical fonts -->
    <link rel="preload" href="https://fonts.gstatic.com/s/inter/v12/UcCO3FwrK3iLTeHuS_fvQtMwCp50KnMw2boKoduKmMEVuLyfAZ9hiA.woff2" as="font" type="font/woff2" crossorigin />
    <link rel="preload" href="https://fonts.gstatic.com/s/inter/v12/UcCO3FwrK3iLTeHuS_fvQtMwCp50KnMw2boKoduKmMEVuGKYAZ9hiA.woff2" as="font" type="font/woff2" crossorigin />
    
    <!-- DNS Prefetch para recursos externos -->
    <link rel="dns-prefetch" href="//fonts.googleapis.com" />
    <link rel="dns-prefetch" href="//fonts.gstatic.com" />
    <link rel="dns-prefetch" href="//ik.imagekit.io" />
    
    <!-- Preconnect para recursos críticos -->
    <link rel="preconnect" href="https://fonts.googleapis.com" crossorigin />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link rel="preconnect" href="https://ik.imagekit.io" crossorigin />
    
    <!-- Resource hints -->
    <link rel="prefetch" href="/catalog" />
    <link rel="prefetch" href="/nosotros" />
    
    <!-- Canonical URL -->
    <link rel="canonical" href="https://cr-seg-ind.pages.dev/" />
    
    <!-- Locale Alternates -->
    <link rel="alternate" href="https://cr-seg-ind.pages.dev/" hreflang="es" />
    <link rel="alternate" href="https://cr-seg-ind.pages.dev/" hreflang="x-default" />
    
    <!-- Performance hints -->
    <meta http-equiv="x-dns-prefetch-control" content="on" />
    <meta name="format-detection" content="telephone=no" />
    
    <!-- PWA Meta -->
    <meta name="theme-color" content="#f59e0b" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent" />
  </head>
  
  <body class="overflow-x-hidden bg-slate-900">
    <!-- Skip to main content para accesibilidad -->
    <a href="#main-content" class="sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 focus:z-50 focus:px-4 focus:py-2 focus:bg-amber-500 focus:text-white focus:rounded">
      Saltar al contenido principal
    </a>
    
    <!-- Critical above-the-fold content -->
    <div id="critical-content" class="loading-visible">
      <!-- Navbar crítico -->
      <nav class="navbar" role="navigation" aria-label="Navegación principal">
        <div class="navbar-content">
          <a href="/" aria-label="CR Work - Inicio">
            <img src="/images/cr-work-logo.webp" alt="CR Work Logo" class="logo" width="120" height="40" />
          </a>
          <div class="flex items-center gap-4">
            <a href="/catalog" class="text-white hover:text-amber-400 transition-colors">Catálogo</a>
            <a href="/nosotros" class="text-white hover:text-amber-400 transition-colors">Nosotros</a>
          </div>
        </div>
      </nav>
      
      <!-- Hero crítico -->
      <main id="main-content" class="hero-container" role="main">
        <div class="hero-bg"></div>
        <div class="hero-content">
          <h1 class="hero-title">
            Seguridad Industrial y<br />
            <span style="background:linear-gradient(90deg,#fbbf24 0%,#f59e0b 100%);-webkit-background-clip:text;-webkit-text-fill-color:transparent;background-clip:text">
              Equipos de Protección
            </span>
          </h1>
          <p class="hero-subtitle">
            Protegemos lo más valioso de tu empresa: tu gente. 
            Equipos de seguridad industrial de primera calidad.
          </p>
          <a href="/catalog" class="hero-cta" aria-label="Ver catálogo de productos">
            Ver Catálogo
            <svg class="ml-2 w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
            </svg>
          </a>
        </div>
      </main>
    </div>
    
    <!-- React App Container -->
    <div id="root"></div>
    
    <!-- Structured Data optimizado -->
    <script type="application/ld+json">
      {
        "@context": "https://schema.org",
        "@type": "Organization",
        "name": "CR Work Seguridad Industrial",
        "url": "https://cr-seg-ind.pages.dev",
        "logo": "https://cr-seg-ind.pages.dev/images/cr-work-logo.webp",
        "description": "Proveedor líder de equipos de protección personal y seguridad industrial en Argentina",
        "address": {
          "@type": "PostalAddress",
          "addressCountry": "AR"
        },
        "contactPoint": {
          "@type": "ContactPoint",
          "contactType": "sales",
          "availableLanguage": ["Spanish"]
        },
        "sameAs": [
          "https://www.instagram.com/crwork.seguridad",
          "https://wa.me/5491234567890"
        ]
      }
    </script>
    
    <!-- Load React App -->
    <script type="module" src="/src/main.tsx"></script>
    
    <!-- Service Worker para caché -->
    <script>
      if ('serviceWorker' in navigator) {
        window.addEventListener('load', () => {
          navigator.serviceWorker.register('/sw.js')
            .then(registration => console.log('SW registered'))
            .catch(error => console.log('SW registration failed'));
        });
      }
    </script>
    
    <!-- Web Vitals monitoring -->
    <script>
      // Monitor LCP
      new PerformanceObserver((entryList) => {
        for (const entry of entryList.getEntries()) {
          if (entry.entryType === 'largest-contentful-paint') {
            console.log('LCP:', entry.startTime);
            // Enviar a analytics si es necesario
          }
        }
      }).observe({entryTypes: ['largest-contentful-paint']});
    </script>
  </body>
</html>
