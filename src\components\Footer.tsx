import React, { useState } from 'react';
import { MapPin, Phone, Mail, Clock, Instagram, Linkedin } from 'lucide-react';
import MapPopup from './MapPopup';
import { VersionInfo } from './VersionInfo';

const Footer = () => {
  const [email, setEmail] = useState('');
  const [isMapOpen, setIsMapOpen] = useState(false);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    console.log('Email subscrito:', email);
    setEmail('');
  };

  return (
    <>
      <footer className="bg-secondary-dark text-gray-300">
        <div className="container mx-auto px-4 py-10"> {/* Aumentado py-6 a py-10 */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6"> {/* Increased gap-4 to gap-6 */}
            {/* <PERSON><PERSON> de Nosotros */}
            <div className="space-y-4"> {/* Increased space-y-2 to space-y-4 */}
              <h3 className="text-base font-semibold text-white">Sobre CR Work</h3>
              <p className="text-sm text-gray-400 leading-relaxed">
                Somos líderes en la comercialización de equipos de protección personal 
                y elementos de seguridad industrial. Nuestro compromiso es garantizar 
                la seguridad y el bienestar de nuestros clientes con productos de la 
                más alta calidad.
              </p>
              {/* Redes Sociales */}
              <div className="flex items-center space-x-6">
                <a 
                  href="mailto:<EMAIL>"
                  className="text-gray-400 hover:text-amber-500 transition-colors"
                >
                  <Mail className="w-8 h-8" />
                </a>
                <a 
                  href="https://instagram.com/crwork.seguridad" 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="text-gray-400 hover:text-amber-500 transition-colors"
                >
                  <Instagram className="w-8 h-8" />
                </a>
                <a 
                  href="https://linkedin.com/company/cr-work" 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="text-gray-400 hover:text-amber-500 transition-colors"
                >
                  <Linkedin className="w-8 h-8" />
                </a>
                <a 
                  href="https://wa.me/541152218882?text=Hola%2C%20vi%20el%20nuevo%20sitio%20web%20y%20me%20interesan%20algunos%20productos." 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="text-gray-400 hover:text-amber-500 transition-colors"
                >
                  <svg 
                    className="w-8 h-8" 
                    fill="currentColor" 
                    viewBox="0 0 24 24" 
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893a11.821 11.821 0 00-3.48-8.413z"/>
                  </svg>
                </a>
              </div>
            </div>

            {/* Información de contacto */}
            <div className="space-y-4"> {/* Increased space-y-2 to space-y-4 */}
              <h3 className="text-base font-semibold text-white">Contacto</h3>
              <div className="space-y-3"> {/* Increased space-y-1.5 to space-y-3 */}
                <button 
                  onClick={() => setIsMapOpen(true)}
                  className="flex items-center space-x-2 hover:text-amber-500 transition-colors w-full text-left"
                >
                  <MapPin className="w-3.5 h-3.5 text-amber-500 flex-shrink-0" /> {/* Reducido tamaño del ícono */}
                  <span className="text-sm">Merlo 2338, Moreno, Buenos Aires</span>
                </button>
                <div className="flex items-center space-x-2">
                  <Phone className="w-3.5 h-3.5 text-amber-500 flex-shrink-0" />
                  <span className="text-sm">0237-4636894</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Mail className="w-3.5 h-3.5 text-amber-500 flex-shrink-0" />
                  <a 
                    href="mailto:<EMAIL>"
                    className="text-sm hover:text-amber-500 transition-colors"
                  >
                    <EMAIL>
                  </a>
                </div>
                <div className="flex items-center space-x-2">
                  <Clock className="w-3.5 h-3.5 text-amber-500 flex-shrink-0" />
                  <span className="text-sm">Lunes a Viernes: 8:30 a 17:30hs</span>
                </div>
              </div>
            </div>

            {/* Área de suscripción */}
            <div className="space-y-4"> {/* Increased space-y-2 to space-y-4 */}
              <h3 className="text-base font-semibold text-white">Novedades</h3>
              <p className="text-sm text-gray-400 leading-relaxed"> {/* Added leading-relaxed */}
                Suscríbete para recibir actualizaciones sobre nuevos productos y ofertas especiales.
              </p>
              <form onSubmit={handleSubmit} className="space-y-3"> {/* Increased space-y-1.5 to space-y-3 */}
                <input
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  placeholder="Tu correo electrónico"
                  className="w-full px-3 py-2 rounded-md bg-secondary-light text-white placeholder-gray-400 border border-gray-600 focus:outline-none focus:border-amber-500"
                  required
                />
                <button
                  type="submit"
                  className="w-full bg-amber-600 hover:bg-amber-700 text-white py-2 px-3 rounded-md transition duration-300"
                >
                  Suscribirse
                </button>
              </form>
            </div>
          </div>
          <div className="mt-6 pt-4 border-t border-gray-700 flex justify-between items-center">
            <div className="text-xs text-gray-400">
              © {new Date().getFullYear()} CR Work. Todos los derechos reservados.
            </div>
            <VersionInfo />
          </div>
        </div>
      </footer>
      
      <MapPopup 
        isOpen={isMapOpen}
        onClose={() => setIsMapOpen(false)}
      />
    </>
  );
};

export default Footer;
