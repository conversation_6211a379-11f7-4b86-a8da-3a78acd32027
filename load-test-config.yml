# ============================================================================
# CONFIGURACIÓN DE LOAD TESTING - CR Work
# Pruebas de carga para validar capacidad de 50+ usuarios simultáneos
# ============================================================================

config:
  target: 'http://localhost:3010'
  phases:
    # Fase 1: Calentamiento (1 minuto, 10 usuarios/seg)
    - duration: 60
      arrivalRate: 10
      name: "Warmup Phase"
    
    # Fase 2: Carga normal (2 minutos, 25 usuarios/seg)
    - duration: 120
      arrivalRate: 25
      name: "Normal Load"
    
    # Fase 3: Carga alta (1 minuto, 50 usuarios/seg)
    - duration: 60
      arrivalRate: 50
      name: "High Load"
    
    # Fase 4: Pico de <PERSON> (30 segundos, 75 usuarios/seg)
    - duration: 30
      arrivalRate: 75
      name: "Peak Load"
    
    # Fase 5: Enfriamiento (30 segundos, 10 usuarios/seg)
    - duration: 30
      arrivalRate: 10
      name: "Cooldown"

  # Configuración por defecto para todas las requests
  defaults:
    headers:
      'User-Agent': 'Artillery Load Test'
      'Accept': 'application/json'
      'Content-Type': 'application/json'

  # Métricas personalizadas
  engines:
    http:
      timeout: 10000  # 10 segundos timeout
      pool: 50        # Pool de conexiones HTTP

  # Plugins para métricas avanzadas
  plugins:
    metrics-by-endpoint:
      useOnlyRequestNames: true

# ============================================================================
# ESCENARIOS DE PRUEBA
# ============================================================================

scenarios:
  # Escenario 1: Health Check (30% del tráfico)
  - name: "Health Check"
    weight: 30
    flow:
      - get:
          url: "/health"
          capture:
            - json: "$.status"
              as: "health_status"
      - think: 1  # Pausa de 1 segundo

  # Escenario 2: Catálogo de Productos (40% del tráfico)
  - name: "Product Catalog"
    weight: 40
    flow:
      # Obtener productos generales
      - get:
          url: "/api/products"
          headers:
            Authorization: "Bearer {{ $randomString() }}"
          expect:
            - statusCode: [200, 401]  # 401 esperado sin auth válido
      
      - think: 2  # Simular tiempo de lectura
      
      # Filtrar por categoría EPP
      - get:
          url: "/api/catalog?category=epp&limit=20"
          headers:
            Authorization: "Bearer {{ $randomString() }}"
          expect:
            - statusCode: [200, 401]
      
      - think: 1

  # Escenario 3: Búsqueda de Productos (20% del tráfico)
  - name: "Product Search"
    weight: 20
    flow:
      # Búsquedas comunes
      - get:
          url: "/api/search?q={{ $randomString() }}&limit=10"
          headers:
            Authorization: "Bearer {{ $randomString() }}"
          expect:
            - statusCode: [200, 401, 429]  # 429 = rate limited
      
      - think: 3
      
      # Búsqueda específica EPP
      - get:
          url: "/api/search?q=casco&category=craneana"
          headers:
            Authorization: "Bearer {{ $randomString() }}"
          expect:
            - statusCode: [200, 401, 429]

  # Escenario 4: Navegación de Imágenes (10% del tráfico)
  - name: "Static Assets"
    weight: 10
    flow:
      # Cargar imágenes estáticas
      - get:
          url: "/images/epp.jpg"
          expect:
            - statusCode: [200, 404]
      
      - get:
          url: "/images/helmet.jpg"
          expect:
            - statusCode: [200, 404]
      
      - think: 0.5

# ============================================================================
# FUNCIONES AUXILIARES
# ============================================================================

# Generar strings aleatorios para simular búsquedas
functions:
  randomSearchTerm:
    - "casco"
    - "guantes"
    - "anteojos"
    - "mascarilla"
    - "botas"
    - "arnes"
    - "proteccion"
    - "seguridad"
    - "epp"
    - "industrial"

# ============================================================================
# CONFIGURACIÓN DE MÉTRICAS
# ============================================================================

# Umbrales de performance esperados
expect:
  # Response time percentiles
  p95: 500    # 95% de requests < 500ms
  p99: 1000   # 99% de requests < 1000ms
  
  # Error rates
  maxErrorRate: 5  # Máximo 5% de errores
  
  # Throughput mínimo
  minThroughput: 100  # Mínimo 100 requests/segundo

# ============================================================================
# CONFIGURACIÓN DE REPORTES
# ============================================================================

# Generar reporte detallado
reporting:
  json: "load-test-results.json"
  html: "load-test-report.html"
