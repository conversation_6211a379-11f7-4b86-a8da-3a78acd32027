// ============================================================================
// SERVICE WORKER OPTIMIZADO PARA LCP
// Cache estratégico para mejorar performance
// ============================================================================

const CACHE_NAME = 'cr-work-v1.2.0';
const STATIC_CACHE = 'cr-work-static-v1.2.0';
const DYNAMIC_CACHE = 'cr-work-dynamic-v1.2.0';
const IMAGE_CACHE = 'cr-work-images-v1.2.0';

// Recursos críticos para caché inmediato
const CRITICAL_ASSETS = [
  '/',
  '/index.html',
  '/manifest.json',
  '/images/cr-work-logo.webp',
  '/images/favicon.ico'
];

// Recursos estáticos para caché
const STATIC_ASSETS = [
  '/catalog',
  '/nosotros',
  '/images/hero-bg.webp',
  '/images/hero-bg-mobile.webp'
];

// URLs de imágenes externas para caché
const EXTERNAL_IMAGES = [
  'https://ik.imagekit.io/crwork/',
  'https://fonts.gstatic.com/',
  'https://fonts.googleapis.com/'
];

// ============================================================================
// INSTALL EVENT - Caché de recursos críticos
// ============================================================================

self.addEventListener('install', (event) => {
  console.log('SW: Installing...');
  
  event.waitUntil(
    Promise.all([
      // Caché crítico
      caches.open(CACHE_NAME).then((cache) => {
        console.log('SW: Caching critical assets');
        return cache.addAll(CRITICAL_ASSETS);
      }),
      
      // Caché estático
      caches.open(STATIC_CACHE).then((cache) => {
        console.log('SW: Caching static assets');
        return cache.addAll(STATIC_ASSETS);
      })
    ]).then(() => {
      console.log('SW: Installation complete');
      // Activar inmediatamente
      return self.skipWaiting();
    })
  );
});

// ============================================================================
// ACTIVATE EVENT - Limpieza de cachés antiguos
// ============================================================================

self.addEventListener('activate', (event) => {
  console.log('SW: Activating...');
  
  event.waitUntil(
    Promise.all([
      // Limpiar cachés antiguos
      caches.keys().then((cacheNames) => {
        return Promise.all(
          cacheNames.map((cacheName) => {
            if (
              cacheName !== CACHE_NAME &&
              cacheName !== STATIC_CACHE &&
              cacheName !== DYNAMIC_CACHE &&
              cacheName !== IMAGE_CACHE
            ) {
              console.log('SW: Deleting old cache:', cacheName);
              return caches.delete(cacheName);
            }
          })
        );
      }),
      
      // Tomar control inmediatamente
      self.clients.claim()
    ]).then(() => {
      console.log('SW: Activation complete');
    })
  );
});

// ============================================================================
// FETCH EVENT - Estrategias de caché optimizadas
// ============================================================================

self.addEventListener('fetch', (event) => {
  const { request } = event;
  const url = new URL(request.url);
  
  // Solo manejar requests HTTP/HTTPS
  if (!request.url.startsWith('http')) {
    return;
  }
  
  // Estrategia por tipo de recurso
  if (request.destination === 'image') {
    event.respondWith(handleImageRequest(request));
  } else if (request.destination === 'document') {
    event.respondWith(handleDocumentRequest(request));
  } else if (request.destination === 'script' || request.destination === 'style') {
    event.respondWith(handleAssetRequest(request));
  } else if (url.pathname.startsWith('/api/')) {
    event.respondWith(handleApiRequest(request));
  } else {
    event.respondWith(handleGenericRequest(request));
  }
});

// ============================================================================
// ESTRATEGIAS DE CACHÉ ESPECÍFICAS
// ============================================================================

// Imágenes: Cache First con fallback
async function handleImageRequest(request) {
  try {
    // Intentar desde caché primero
    const cachedResponse = await caches.match(request);
    if (cachedResponse) {
      return cachedResponse;
    }
    
    // Si no está en caché, fetch y guardar
    const response = await fetch(request);
    
    if (response.ok) {
      const cache = await caches.open(IMAGE_CACHE);
      // Solo cachear imágenes < 5MB
      const contentLength = response.headers.get('content-length');
      if (!contentLength || parseInt(contentLength) < 5 * 1024 * 1024) {
        cache.put(request, response.clone());
      }
    }
    
    return response;
  } catch (error) {
    console.log('SW: Image fetch failed:', error);
    // Fallback a imagen placeholder
    return caches.match('/images/placeholder-product.jpg');
  }
}

// Documentos: Network First con caché fallback
async function handleDocumentRequest(request) {
  try {
    // Intentar network primero para contenido fresco
    const response = await fetch(request);
    
    if (response.ok) {
      const cache = await caches.open(DYNAMIC_CACHE);
      cache.put(request, response.clone());
    }
    
    return response;
  } catch (error) {
    console.log('SW: Document fetch failed, trying cache:', error);
    
    // Fallback a caché
    const cachedResponse = await caches.match(request);
    if (cachedResponse) {
      return cachedResponse;
    }
    
    // Fallback final a página offline
    return caches.match('/');
  }
}

// Assets (JS/CSS): Cache First con revalidación en background
async function handleAssetRequest(request) {
  try {
    const cachedResponse = await caches.match(request);
    
    if (cachedResponse) {
      // Revalidar en background
      fetch(request).then((response) => {
        if (response.ok) {
          const cache = caches.open(STATIC_CACHE);
          cache.then(c => c.put(request, response));
        }
      }).catch(() => {
        // Ignorar errores de background fetch
      });
      
      return cachedResponse;
    }
    
    // Si no está en caché, fetch y guardar
    const response = await fetch(request);
    
    if (response.ok) {
      const cache = await caches.open(STATIC_CACHE);
      cache.put(request, response.clone());
    }
    
    return response;
  } catch (error) {
    console.log('SW: Asset fetch failed:', error);
    throw error;
  }
}

// API: Network Only con timeout
async function handleApiRequest(request) {
  try {
    // Timeout de 10 segundos para APIs
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 10000);
    
    const response = await fetch(request, {
      signal: controller.signal
    });
    
    clearTimeout(timeoutId);
    return response;
  } catch (error) {
    console.log('SW: API fetch failed:', error);
    
    // Respuesta de error estructurada
    return new Response(
      JSON.stringify({
        error: 'Network error',
        message: 'Unable to fetch data. Please check your connection.',
        offline: true
      }),
      {
        status: 503,
        statusText: 'Service Unavailable',
        headers: {
          'Content-Type': 'application/json'
        }
      }
    );
  }
}

// Requests genéricos: Stale While Revalidate
async function handleGenericRequest(request) {
  try {
    const cache = await caches.open(DYNAMIC_CACHE);
    const cachedResponse = await cache.match(request);
    
    // Fetch en paralelo
    const fetchPromise = fetch(request).then((response) => {
      if (response.ok) {
        cache.put(request, response.clone());
      }
      return response;
    });
    
    // Devolver caché si existe, sino esperar fetch
    return cachedResponse || await fetchPromise;
  } catch (error) {
    console.log('SW: Generic fetch failed:', error);
    
    // Intentar desde cualquier caché
    const response = await caches.match(request);
    if (response) {
      return response;
    }
    
    throw error;
  }
}

// ============================================================================
// BACKGROUND SYNC (opcional)
// ============================================================================

self.addEventListener('sync', (event) => {
  if (event.tag === 'background-sync') {
    event.waitUntil(doBackgroundSync());
  }
});

async function doBackgroundSync() {
  try {
    // Sincronizar datos pendientes cuando hay conexión
    console.log('SW: Background sync triggered');
    
    // Aquí puedes agregar lógica para sincronizar datos offline
    // Por ejemplo, enviar formularios guardados, etc.
    
  } catch (error) {
    console.log('SW: Background sync failed:', error);
  }
}

// ============================================================================
// PUSH NOTIFICATIONS (opcional)
// ============================================================================

self.addEventListener('push', (event) => {
  if (event.data) {
    const data = event.data.json();
    
    const options = {
      body: data.body,
      icon: '/images/icon-192x192.png',
      badge: '/images/badge-72x72.png',
      vibrate: [100, 50, 100],
      data: {
        dateOfArrival: Date.now(),
        primaryKey: data.primaryKey
      },
      actions: [
        {
          action: 'explore',
          title: 'Ver más',
          icon: '/images/checkmark.png'
        },
        {
          action: 'close',
          title: 'Cerrar',
          icon: '/images/xmark.png'
        }
      ]
    };
    
    event.waitUntil(
      self.registration.showNotification(data.title, options)
    );
  }
});

// ============================================================================
// UTILIDADES
// ============================================================================

// Limpiar caché cuando sea necesario
async function cleanupCache() {
  const cacheNames = await caches.keys();
  
  for (const cacheName of cacheNames) {
    const cache = await caches.open(cacheName);
    const requests = await cache.keys();
    
    // Limpiar entradas antiguas (más de 7 días)
    const oneWeekAgo = Date.now() - (7 * 24 * 60 * 60 * 1000);
    
    for (const request of requests) {
      const response = await cache.match(request);
      const dateHeader = response?.headers.get('date');
      
      if (dateHeader) {
        const responseDate = new Date(dateHeader).getTime();
        if (responseDate < oneWeekAgo) {
          await cache.delete(request);
        }
      }
    }
  }
}

// Ejecutar limpieza periódicamente
setInterval(cleanupCache, 24 * 60 * 60 * 1000); // Cada 24 horas
