import { defineConfig, loadEnv } from 'vite';
import react from '@vitejs/plugin-react';
import path from 'path';

// ============================================================================
// VITE CONFIG OPTIMIZADO PARA LCP < 1.5s
// ============================================================================

// Determinar la base URL según el ambiente
const getBase = () => {
  if (process.env.NODE_ENV === 'development') {
    return '/';
  }
  if (process.env.CF_PAGES_BRANCH === 'release') {
    return '/release-v1-0-0/';
  }
  return '/';
};

export default defineConfig(({ mode }) => {
  const env = loadEnv(mode, process.cwd(), '');
  const isProduction = mode === 'production';
  
  return {
    plugins: [
      react({
        // Optimizaciones de React
        babel: {
          plugins: isProduction ? [
            // Remover PropTypes en producción
            ['babel-plugin-transform-remove-console', { exclude: ['error', 'warn'] }],
            // Optimizar imports
            ['babel-plugin-import', {
              libraryName: 'lucide-react',
              libraryDirectory: 'dist/esm/icons',
              camel2DashComponentName: false
            }, 'lucide-react']
          ] : []
        }
      })
    ],
    
    server: {
      port: 5173,
      host: true,
      proxy: {
        '/api': {
          target: 'http://localhost:3010',
          changeOrigin: true,
          secure: false,
          rewrite: (path) => path.replace(/^\/api/, '')
        }
      }
    },

    build: {
      outDir: 'dist',
      sourcemap: !isProduction, // Solo en desarrollo
      
      // Optimizaciones críticas para LCP
      rollupOptions: {
        output: {
          // Code splitting optimizado
          manualChunks: {
            // Vendor chunks separados
            'react-vendor': ['react', 'react-dom'],
            'router-vendor': ['react-router-dom'],
            'ui-vendor': ['framer-motion', 'lucide-react'],
            'query-vendor': ['@tanstack/react-query'],
            'supabase-vendor': ['@supabase/supabase-js'],
            
            // Chunks por funcionalidad
            'auth': [
              './src/context/AuthContext.tsx',
              './src/components/UserProfile.tsx',
              './src/components/Orders.tsx'
            ],
            'catalog': [
              './src/components/Catalog.tsx',
              './src/components/ProductCard.tsx',
              './src/components/ProductDetail.tsx'
            ],
            'admin': [
              './src/routes/AdminRoutes.tsx',
              './src/components/AdminPanel.tsx'
            ]
          },
          
          // Optimizar nombres de archivos
          chunkFileNames: (chunkInfo) => {
            const facadeModuleId = chunkInfo.facadeModuleId 
              ? chunkInfo.facadeModuleId.split('/').pop().replace('.tsx', '').replace('.ts', '')
              : 'chunk';
            return `js/${facadeModuleId}-[hash].js`;
          },
          assetFileNames: (assetInfo) => {
            const info = assetInfo.name.split('.');
            const ext = info[info.length - 1];
            if (/png|jpe?g|svg|gif|tiff|bmp|ico/i.test(ext)) {
              return `images/[name]-[hash][extname]`;
            }
            if (/css/i.test(ext)) {
              return `css/[name]-[hash][extname]`;
            }
            return `assets/[name]-[hash][extname]`;
          }
        }
      },
      
      // Optimizaciones de minificación
      minify: 'terser',
      terserOptions: {
        compress: {
          drop_console: isProduction,
          drop_debugger: isProduction,
          pure_funcs: isProduction ? ['console.log', 'console.debug'] : [],
          // Optimizaciones agresivas para producción
          passes: 2,
          unsafe_arrows: true,
          unsafe_methods: true,
          unsafe_proto: true
        },
        mangle: {
          safari10: true
        },
        format: {
          comments: false
        }
      },
      
      // Configuración de chunks
      chunkSizeWarningLimit: 1000, // 1MB warning
      
      // Optimizaciones de assets
      assetsInlineLimit: 4096, // 4KB inline limit
      
      // CSS Code splitting
      cssCodeSplit: true,
      
      // Reportar bundle size
      reportCompressedSize: true
    },

    // Optimizaciones de dependencias
    optimizeDeps: {
      include: [
        'react',
        'react-dom',
        'react-router-dom',
        '@tanstack/react-query',
        'framer-motion',
        'lucide-react'
      ],
      exclude: [
        // Excluir dependencias que no se usan en el critical path
        '@supabase/supabase-js'
      ]
    },

    resolve: {
      alias: {
        '@': path.resolve(__dirname, './src')
      }
    },

    base: getBase(),

    // Configuraciones adicionales para performance
    esbuild: {
      // Optimizaciones de esbuild
      legalComments: 'none',
      minifyIdentifiers: isProduction,
      minifySyntax: isProduction,
      minifyWhitespace: isProduction,
      treeShaking: true
    },

    // CSS optimizations
    css: {
      devSourcemap: !isProduction,
      preprocessorOptions: {
        // Optimizaciones de CSS
      }
    },

    // Configuración de preview
    preview: {
      port: 4173,
      host: true
    },

    // Variables de entorno
    define: {
      __DEV__: !isProduction,
      __PROD__: isProduction
    }
  };
});
