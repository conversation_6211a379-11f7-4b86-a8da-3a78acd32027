import React, { useState, useEffect, Suspense, lazy } from 'react';
import { Routes, Route, useLocation } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { AuthProvider } from './context/AuthContext';

// ============================================================================
// IMPORTS CRÍTICOS - Solo lo necesario para el first paint
// ============================================================================

// Componentes críticos (no lazy)
import Navbar from './components/Navbar';
import HeroOptimized from './components/HeroOptimized';
import Footer from './components/Footer';

// ============================================================================
// LAZY LOADING OPTIMIZADO - Componentes no críticos
// ============================================================================

// Componentes above-the-fold (prioridad alta)
const PromoSlider = lazy(() => import('./components/PromoSlider'));
const BrandCarousel = lazy(() => import('./components/BrandCarousel'));

// Componentes below-the-fold (prioridad media)
const IndustriesSection = lazy(() => import('./components/IndustriesSection'));
const StatsSection = lazy(() => import('./components/StatsSection'));
const PPECategoryVisualizer = lazy(() => import('./components/PPECategoryVisualizer'));
const CtaCards = lazy(() => import('./components/CtaCards'));

// Componentes de páginas (prioridad baja)
const Catalog = lazy(() => import('./components/Catalog'));
const ProductDetail = lazy(() => import('./components/ProductDetail'));
const UserRegistration = lazy(() => import('./components/UserRegistration'));
const Nosotros = lazy(() => import('./components/Nosotros'));
const UserProfile = lazy(() => import('./components/UserProfile'));
const Orders = lazy(() => import('./components/Orders'));
const OrderDetail = lazy(() => import('./components/OrderDetail'));

// Componentes admin (carga bajo demanda)
const AdminRoutes = lazy(() => import('./routes/AdminRoutes'));
const ProtectedRoute = lazy(() => import('./components/ProtectedRoute'));

// Componentes de UI
const Cart = lazy(() => import('./components/Cart'));
const ScrollToTopButton = lazy(() => import('./components/ScrollToTopButton'));
const WhatsAppButton = lazy(() => import('./components/WhatsAppButton'));

// ============================================================================
// CONFIGURACIÓN OPTIMIZADA
// ============================================================================

// QueryClient optimizado
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 5 * 60 * 1000, // 5 minutos
      cacheTime: 10 * 60 * 1000, // 10 minutos
      retry: 1, // Menos reintentos para mejor performance
      refetchOnWindowFocus: false, // Evitar refetch innecesario
    },
  },
});

// ============================================================================
// COMPONENTE DE LOADING OPTIMIZADO
// ============================================================================

const OptimizedSuspense: React.FC<{ children: React.ReactNode; fallback?: React.ReactNode }> = ({ 
  children, 
  fallback 
}) => (
  <Suspense 
    fallback={
      fallback || (
        <div className="flex items-center justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-amber-500"></div>
        </div>
      )
    }
  >
    {children}
  </Suspense>
);

// ============================================================================
// COMPONENTE PRINCIPAL OPTIMIZADO
// ============================================================================

function AppOptimized() {
  const location = useLocation();
  const isHomePage = location.pathname === '/';
  
  // Estados mínimos necesarios
  const [cartItems, setCartItems] = useState([]);
  const [isCartOpen, setIsCartOpen] = useState(false);

  // Preload crítico solo para home
  useEffect(() => {
    if (isHomePage) {
      // Preload componentes críticos de home
      const preloadHomeComponents = async () => {
        try {
          await Promise.all([
            import('./components/PromoSlider'),
            import('./components/BrandCarousel')
          ]);
        } catch (error) {
          console.warn('Preload failed:', error);
        }
      };
      
      // Preload después del primer render
      const timer = setTimeout(preloadHomeComponents, 100);
      return () => clearTimeout(timer);
    }
  }, [isHomePage]);

  // Intersection Observer para lazy loading de secciones
  useEffect(() => {
    if (!isHomePage) return;

    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            const target = entry.target as HTMLElement;
            const componentName = target.dataset.component;
            
            // Preload componente cuando está cerca del viewport
            if (componentName === 'industries') {
              import('./components/IndustriesSection');
            } else if (componentName === 'stats') {
              import('./components/StatsSection');
            } else if (componentName === 'ppe-visualizer') {
              import('./components/PPECategoryVisualizer');
            }
          }
        });
      },
      { rootMargin: '200px' } // Preload 200px antes de que sea visible
    );

    // Observar elementos placeholder
    const placeholders = document.querySelectorAll('[data-component]');
    placeholders.forEach((el) => observer.observe(el));

    return () => observer.disconnect();
  }, [isHomePage]);

  // Handlers optimizados
  const handleAddToCart = (product: any) => {
    setCartItems(prev => {
      const existingItem = prev.find(item => item.id === product.id);
      if (existingItem) {
        return prev.map(item =>
          item.id === product.id
            ? { ...item, quantity: item.quantity + 1 }
            : item
        );
      }
      return [...prev, { ...product, quantity: 1 }];
    });
  };

  return (
    <QueryClientProvider client={queryClient}>
      <AuthProvider>
        <div className="app flex flex-col min-h-screen">
          {/* Navbar crítico */}
          <Navbar
            onCartClick={() => setIsCartOpen(true)}
            cartItemsCount={cartItems.length}
          />

          {/* Main content */}
          <main className="flex-grow">
            <Routes>
              {/* Home route optimizada */}
              <Route
                path="/"
                element={
                  <>
                    {/* Hero crítico - renderizado inmediatamente */}
                    <HeroOptimized />
                    
                    {/* Componentes lazy con placeholders */}
                    <OptimizedSuspense>
                      <PromoSlider />
                    </OptimizedSuspense>
                    
                    <OptimizedSuspense>
                      <BrandCarousel />
                    </OptimizedSuspense>
                    
                    {/* Placeholder para Industries con lazy loading */}
                    <div data-component="industries" className="min-h-[400px]">
                      <OptimizedSuspense>
                        <IndustriesSection />
                      </OptimizedSuspense>
                    </div>
                    
                    {/* Placeholder para Stats */}
                    <div data-component="stats" className="min-h-[300px]">
                      <OptimizedSuspense>
                        <StatsSection />
                      </OptimizedSuspense>
                    </div>
                    
                    {/* Placeholder para PPE Visualizer */}
                    <div data-component="ppe-visualizer" className="min-h-[500px]">
                      <OptimizedSuspense>
                        <PPECategoryVisualizer />
                      </OptimizedSuspense>
                    </div>
                    
                    <OptimizedSuspense>
                      <CtaCards />
                    </OptimizedSuspense>
                  </>
                }
              />
              
              {/* Otras rutas con lazy loading */}
              <Route
                path="/catalog"
                element={
                  <OptimizedSuspense fallback={
                    <div className="container mx-auto px-4 py-8">
                      <div className="animate-pulse space-y-4">
                        <div className="h-8 bg-gray-200 rounded w-1/4"></div>
                        <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-6">
                          {[...Array(8)].map((_, i) => (
                            <div key={i} className="h-64 bg-gray-200 rounded"></div>
                          ))}
                        </div>
                      </div>
                    </div>
                  }>
                    <Catalog onAddToCart={handleAddToCart} />
                  </OptimizedSuspense>
                }
              />
              
              <Route
                path="/product/:id"
                element={
                  <OptimizedSuspense>
                    <ProductDetail onAddToCart={handleAddToCart} />
                  </OptimizedSuspense>
                }
              />
              
              <Route
                path="/register"
                element={
                  <OptimizedSuspense>
                    <UserRegistration />
                  </OptimizedSuspense>
                }
              />
              
              <Route
                path="/nosotros"
                element={
                  <OptimizedSuspense>
                    <Nosotros />
                  </OptimizedSuspense>
                }
              />
              
              <Route
                path="/order-detail"
                element={
                  <OptimizedSuspense>
                    <OrderDetail items={cartItems} />
                  </OptimizedSuspense>
                }
              />
              
              {/* Rutas protegidas */}
              <Route
                element={
                  <OptimizedSuspense>
                    <ProtectedRoute />
                  </OptimizedSuspense>
                }
              >
                <Route
                  path="/admin/*"
                  element={
                    <OptimizedSuspense>
                      <AdminRoutes />
                    </OptimizedSuspense>
                  }
                />
                <Route
                  path="/perfil"
                  element={
                    <OptimizedSuspense>
                      <UserProfile />
                    </OptimizedSuspense>
                  }
                />
                <Route
                  path="/pedidos"
                  element={
                    <OptimizedSuspense>
                      <Orders />
                    </OptimizedSuspense>
                  }
                />
              </Route>
            </Routes>
          </main>

          {/* Footer crítico */}
          <Footer />

          {/* Componentes de UI lazy */}
          <OptimizedSuspense>
            <Cart
              isOpen={isCartOpen}
              onClose={() => setIsCartOpen(false)}
              items={cartItems}
              onUpdateQuantity={(id, quantity) => {
                setCartItems(prev =>
                  prev.map(item =>
                    item.id === id ? { ...item, quantity } : item
                  )
                );
              }}
              onRemoveItem={(id) => {
                setCartItems(prev => prev.filter(item => item.id !== id));
              }}
            />
          </OptimizedSuspense>

          <OptimizedSuspense>
            <ScrollToTopButton />
          </OptimizedSuspense>

          <OptimizedSuspense>
            <WhatsAppButton />
          </OptimizedSuspense>
        </div>
      </AuthProvider>
    </QueryClientProvider>
  );
}

export default AppOptimized;
