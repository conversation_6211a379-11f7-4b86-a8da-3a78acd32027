import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Cargar variables de entorno
dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Faltan variables de entorno VITE_SUPABASE_URL o VITE_SUPABASE_ANON_KEY');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

// ============================================================================
// ESTRUCTURA COMPLETA DE EPP - 3 NIVELES
// ============================================================================

// Nivel 1: EPP (categoría principal)
// Nivel 2: Tipos de protección (craneana, ocular, etc.)
// Nivel 3: Subcategorías específicas (dieléctricos, altura, etc.)

// NIVEL 2: Tipos de protección EPP con tags y propiedades base
const eppCategoryData = {
  'craneana': {
    tags: ['seguridad', 'cabeza', 'impacto', 'construccion', 'casco'],
    baseProperties: {
      tipo_proteccion: 'Craneana',
      normativa: ['ANSI Z89.1', 'EN 397', 'EN 812'],
      material: ['ABS', 'Polietileno', 'HDPE'],
      resistencia_impacto: 'Alta',
      certificacion: ['CE', 'ANSI'],
      uso_recomendado: ['Construcción', 'Industria', 'Minería']
    }
  },
  'ocular': {
    tags: ['vision', 'proteccion', 'ojos', 'transparente', 'laboratorio'],
    baseProperties: {
      tipo_proteccion: 'Ocular',
      normativa: ['ANSI Z87.1', 'EN 166'],
      material: ['Policarbonato', 'Acetato'],
      resistencia_impacto: 'Media',
      certificacion: ['CE', 'ANSI'],
      uso_recomendado: ['Laboratorio', 'Industria', 'Construcción']
    }
  },
  'facial': {
    tags: ['rostro', 'cara', 'salpicaduras', 'quimica', 'soldadura', 'protector'],
    baseProperties: {
      tipo_proteccion: 'Facial',
      normativa: ['ANSI Z87.1', 'EN 166', 'EN 1731', 'NFPA 70E'],
      material: ['Policarbonato', 'Acetato', 'Propionato'],
      resistencia_impacto: 'Alta',
      certificacion: ['CE', 'ANSI', 'NFPA'],
      uso_recomendado: ['Soldadura', 'Química', 'Forestal', 'Eléctrico']
    }
  },
  'respiratoria': {
    tags: ['aire', 'respiracion', 'filtros', 'particulas', 'gases', 'mascarilla'],
    baseProperties: {
      tipo_proteccion: 'Respiratoria',
      normativa: ['NIOSH N95', 'EN 14387', 'NFPA 1981'],
      material: ['Polipropileno', 'Silicona', 'TPE'],
      eficiencia_filtracion: '95%',
      certificacion: ['NIOSH', 'CE'],
      uso_recomendado: ['Industria', 'Construcción', 'Química', 'Salud']
    }
  },
  'indumentaria': {
    tags: ['ropa', 'trabajo', 'comodidad', 'durabilidad', 'uniforme'],
    baseProperties: {
      tipo_proteccion: 'Corporal',
      normativa: ['EN ISO 13688', 'EN 342', 'NFPA 2112'],
      material: ['Algodón', 'Poliéster', 'Nomex', 'Kevlar'],
      talla: ['XS', 'S', 'M', 'L', 'XL', 'XXL', 'XXXL'],
      certificacion: ['CE', 'NFPA'],
      uso_recomendado: ['Industria', 'Construcción', 'Soldadura', 'Química']
    }
  },
  'manos': {
    tags: ['guantes', 'manos', 'agarre', 'destreza', 'proteccion'],
    baseProperties: {
      tipo_proteccion: 'Manos',
      normativa: ['EN 388', 'EN 407', 'EN 511', 'EN 374'],
      material: ['Nitrilo', 'Cuero', 'Kevlar', 'PVC', 'Látex'],
      talla: ['XS', 'S', 'M', 'L', 'XL', 'XXL'],
      certificacion: ['CE'],
      uso_recomendado: ['Construcción', 'Química', 'Mecánica', 'Soldadura']
    }
  },
  'calzado': {
    tags: ['pies', 'calzado', 'antideslizante', 'puntera', 'comodidad', 'seguridad'],
    baseProperties: {
      tipo_proteccion: 'Pies',
      normativa: ['ASTM F2413', 'EN ISO 20345'],
      material: ['Cuero', 'Sintético', 'PVC', 'Poliuretano'],
      puntera: ['Acero', 'Composite', 'Aluminio'],
      certificacion: ['ASTM', 'CE'],
      uso_recomendado: ['Construcción', 'Industria', 'Minería', 'Química']
    }
  },
  'auditiva': {
    tags: ['ruido', 'oidos', 'auditiva', 'concentracion', 'confort'],
    baseProperties: {
      tipo_proteccion: 'Auditiva',
      normativa: ['ANSI S3.19', 'EN 352'],
      material: ['Espuma', 'Silicona', 'PVC'],
      reduccion_ruido: '25-35 dB',
      certificacion: ['ANSI', 'CE'],
      uso_recomendado: ['Industria', 'Construcción', 'Aeropuerto', 'Manufactura']
    }
  },
  'contra-fuego': {
    tags: ['ignifugo', 'fuego', 'calor', 'llamas', 'emergencia', 'bombero'],
    baseProperties: {
      tipo_proteccion: 'Contra Fuego',
      normativa: ['NFPA 1971', 'NFPA 2112', 'EN 469'],
      material: ['Nomex', 'Kevlar', 'PBI', 'Aramida'],
      resistencia_termica: 'Alta',
      certificacion: ['NFPA', 'CE'],
      uso_recomendado: ['Bomberos', 'Soldadura', 'Fundición', 'Petróleo']
    }
  },
  'altura': {
    tags: ['altura', 'arnes', 'caidas', 'seguridad', 'rescate', 'trabajo'],
    baseProperties: {
      tipo_proteccion: 'Altura',
      normativa: ['ANSI Z359.11', 'ANSI Z359.13', 'ANSI Z359.14'],
      material: ['Poliéster', 'Nylon', 'Acero'],
      resistencia_ruptura: '22kN',
      certificacion: ['ANSI', 'CE'],
      uso_recomendado: ['Construcción', 'Torres', 'Minería', 'Rescate']
    }
  }
};

// NIVEL 3: Subcategorías específicas con propiedades detalladas
const eppSubcategoryData = {
  // PROTECCIÓN CRANEANA
  'dielectricos': {
    tags: ['dielectrico', 'electricidad', 'aislante', 'voltaje'],
    properties: {
      subcategoria: 'Cascos Dieléctricos',
      normativa: ['ANSI Z89.1 Clase E', 'IEC 60903'],
      voltaje_max: '20000V',
      resistencia_electrica: 'Clase E',
      color: ['Amarillo', 'Naranja', 'Rojo'],
      aplicacion: ['Electricidad', 'Líneas de Alta Tensión', 'Subestaciones']
    }
  },
  'altura': {
    tags: ['altura', 'trabajo', 'suspension', 'barbijo'],
    properties: {
      subcategoria: 'Cascos de Altura',
      normativa: ['EN 397', 'ANSI Z89.1'],
      barbijo: true,
      ventilacion: true,
      color: ['Blanco', 'Amarillo', 'Azul'],
      aplicacion: ['Torres', 'Construcción en Altura', 'Montaje']
    }
  },
  'gorra-casquete': {
    tags: ['gorra', 'ligero', 'ventilado', 'comodidad'],
    properties: {
      subcategoria: 'Gorra con Casquete',
      normativa: ['EN 812'],
      peso: '200-300g',
      ventilacion: true,
      color: ['Azul', 'Gris', 'Verde'],
      aplicacion: ['Almacenes', 'Oficinas', 'Áreas de Bajo Riesgo']
    }
  },
  'mentoneras': {
    tags: ['mentonera', 'sujecion', 'accesorio', 'ajuste'],
    properties: {
      subcategoria: 'Mentoneras',
      normativa: ['EN 397'],
      material: ['Nylon', 'Poliéster'],
      ajustable: true,
      aplicacion: ['Complemento para Cascos', 'Trabajo en Altura']
    }
  },

  // PROTECCIÓN OCULAR
  'anteojos': {
    tags: ['anteojos', 'lentes', 'uv', 'impacto'],
    properties: {
      subcategoria: 'Anteojos de Seguridad',
      normativa: ['ANSI Z87.1', 'EN 166'],
      proteccion_uv: '99.9%',
      antiempañante: true,
      color: ['Transparente', 'Gris', 'Amarillo'],
      aplicacion: ['Laboratorio', 'Construcción', 'Mecánica']
    }
  },
  'antiparras': {
    tags: ['antiparras', 'hermetico', 'quimica', 'salpicaduras'],
    properties: {
      subcategoria: 'Antiparras',
      normativa: ['EN 166', 'ANSI Z87.1'],
      hermeticidad: 'IP65',
      ventilacion_indirecta: true,
      resistencia_quimica: true,
      aplicacion: ['Química', 'Laboratorio', 'Limpieza Industrial']
    }
  },

  // PROTECCIÓN FACIAL
  'plano': {
    tags: ['plano', 'basico', 'economico', 'general'],
    properties: {
      subcategoria: 'Protector Facial Plano',
      normativa: ['ANSI Z87.1'],
      espesor: '1.0mm',
      campo_vision: '180°',
      aplicacion: ['Uso General', 'Mecánica', 'Carpintería']
    }
  },
  'burbuja': {
    tags: ['burbuja', 'envolvente', 'panoramico', 'cobertura'],
    properties: {
      subcategoria: 'Protector Facial Burbuja',
      normativa: ['EN 166'],
      campo_vision: '200°',
      resistencia_quimica: true,
      aplicacion: ['Química', 'Laboratorio', 'Soldadura']
    }
  },
  'forestal': {
    tags: ['forestal', 'malla', 'astillas', 'sierra'],
    properties: {
      subcategoria: 'Protector Facial Forestal',
      normativa: ['EN 1731'],
      material: 'Malla metálica',
      resistencia_impacto: 'Muy Alta',
      aplicacion: ['Forestal', 'Jardinería', 'Uso de Motosierras']
    }
  },
  'deflagatoria': {
    tags: ['arco', 'electrico', 'deflagracion', 'alta_energia'],
    properties: {
      subcategoria: 'Protección Deflagatoria',
      normativa: ['NFPA 70E', 'ASTM F2178'],
      nivel_arco: 'ATPV 40 cal/cm²',
      resistencia_termica: 'Extrema',
      aplicacion: ['Arco Eléctrico', 'Subestaciones', 'Mantenimiento Eléctrico']
    }
  },
  'soldador': {
    tags: ['soldadura', 'careta', 'radiacion', 'chispas'],
    properties: {
      subcategoria: 'Careta de Soldador',
      normativa: ['ANSI Z87.1', 'EN 175'],
      filtro_optico: 'DIN 9-13',
      auto_oscurecimiento: true,
      aplicacion: ['Soldadura', 'Corte', 'Plasma']
    }
  }
};

// Agregar más subcategorías para completar la estructura
Object.assign(eppSubcategoryData, {
  // PROTECCIÓN RESPIRATORIA
  'mascarillas-descartables': {
    tags: ['mascarilla', 'descartable', 'n95', 'particulas'],
    properties: {
      subcategoria: 'Mascarillas Descartables',
      normativa: ['NIOSH N95', 'EN 149'],
      eficiencia: '95%',
      descartable: true,
      aplicacion: ['Construcción', 'Pintura', 'Limpieza']
    }
  },
  'mascaras': {
    tags: ['mascara', 'filtros', 'gases', 'vapores'],
    properties: {
      subcategoria: 'Máscaras Respiratorias',
      normativa: ['EN 14387', 'NIOSH'],
      tipo_filtro: ['A', 'B', 'E', 'K', 'P'],
      reutilizable: true,
      aplicacion: ['Química', 'Pintura', 'Soldadura']
    }
  },
  'equipos-autonomos': {
    tags: ['autonomo', 'aire', 'emergencia', 'rescate'],
    properties: {
      subcategoria: 'Equipos Autónomos',
      normativa: ['NFPA 1981'],
      autonomia: '30-60 min',
      presion: '300 bar',
      aplicacion: ['Bomberos', 'Rescate', 'Espacios Confinados']
    }
  },

  // PROTECCIÓN DE MANOS
  'anticorte': {
    tags: ['anticorte', 'corte', 'abrasion', 'kevlar'],
    properties: {
      subcategoria: 'Guantes Anticorte',
      normativa: ['EN 388'],
      nivel_corte: ['1', '2', '3', '4', '5'],
      material: ['Kevlar', 'HPPE', 'Acero'],
      aplicacion: ['Vidrio', 'Metal', 'Construcción']
    }
  },
  'temperatura': {
    tags: ['temperatura', 'calor', 'frio', 'termico'],
    properties: {
      subcategoria: 'Guantes para Temperatura',
      normativa: ['EN 407', 'EN 511'],
      temp_max: '250°C',
      temp_min: '-30°C',
      aplicacion: ['Soldadura', 'Fundición', 'Frigoríficos']
    }
  },
  'impacto': {
    tags: ['impacto', 'vibracion', 'mecanico', 'tpr'],
    properties: {
      subcategoria: 'Guantes Anti-impacto',
      normativa: ['EN 388'],
      proteccion_dorso: true,
      material: ['TPR', 'Neopreno'],
      aplicacion: ['Mecánica', 'Construcción', 'Minería']
    }
  },
  'nitrilicos': {
    tags: ['nitrilo', 'quimica', 'resistente', 'descartable'],
    properties: {
      subcategoria: 'Guantes Nitrílicos',
      normativa: ['EN 374'],
      resistencia_quimica: true,
      sin_polvo: true,
      aplicacion: ['Laboratorio', 'Química', 'Alimentaria']
    }
  },

  // CALZADO DE SEGURIDAD
  'zapatos': {
    tags: ['zapatos', 'basico', 'industrial', 'puntera'],
    properties: {
      subcategoria: 'Zapatos de Seguridad',
      normativa: ['ASTM F2413'],
      puntera: ['Acero', 'Composite'],
      plantilla: 'Antiperforation',
      aplicacion: ['Industria General', 'Almacenes']
    }
  },
  'botinas': {
    tags: ['botinas', 'tobillo', 'soporte', 'construccion'],
    properties: {
      subcategoria: 'Botinas',
      normativa: ['EN ISO 20345'],
      altura: 'Media caña',
      soporte_tobillo: true,
      aplicacion: ['Construcción', 'Minería', 'Industria Pesada']
    }
  },
  'zapatillas': {
    tags: ['zapatillas', 'deportivo', 'liviano', 'comodo'],
    properties: {
      subcategoria: 'Zapatillas de Seguridad',
      normativa: ['EN ISO 20345'],
      peso: 'Liviano',
      transpirable: true,
      aplicacion: ['Almacenes', 'Logística', 'Servicios']
    }
  }
});

// Mapeo de compatibilidad con el código existente
const categoryTagsMap = {};
const categoryPropertiesMap = {};

// Llenar los mapas de compatibilidad desde eppCategoryData
Object.keys(eppCategoryData).forEach(category => {
  categoryTagsMap[category] = eppCategoryData[category].tags;
  categoryPropertiesMap[category] = eppCategoryData[category].baseProperties;
});

// Marcas y sus tags específicos
const brandTagsMap = {
  '3M': ['innovacion', 'calidad', 'tecnologia', 'confiable'],
  'MSA': ['profesional', 'resistente', 'certificado'],
  'HONEYWELL': ['precision', 'durabilidad', 'ergonomico'],
  'ANSELL': ['especializado', 'quimica', 'laboratorio'],
  'UVEX': ['vision', 'comodidad', 'deportivo']
};

// Función para determinar el nivel de categorización EPP
function determineEPPLevel(categories) {
  if (!categories || !Array.isArray(categories)) return null;

  // Buscar categorías EPP en los 3 niveles
  const eppLevels = {
    level1: null, // 'epp'
    level2: null, // 'craneana', 'ocular', etc.
    level3: null  // 'dielectricos', 'altura', etc.
  };

  categories.forEach(category => {
    if (category === 'epp') {
      eppLevels.level1 = category;
    } else if (eppCategoryData[category]) {
      eppLevels.level2 = category;
    } else if (eppSubcategoryData[category]) {
      eppLevels.level3 = category;
    }
  });

  return eppLevels;
}

async function populateProductTagsAndProperties() {
  console.log('🚀 Iniciando población de tags y propiedades EPP (3 niveles)...');

  try {
    // 1. Obtener todos los productos
    const { data: products, error: fetchError } = await supabase
      .from('products')
      .select('*');

    if (fetchError) {
      throw fetchError;
    }

    console.log(`📦 Encontrados ${products.length} productos`);

    // 2. Procesar cada producto
    let updatedCount = 0;
    let errorCount = 0;
    let eppProductsCount = 0;

    for (const product of products) {
      try {
        const tags = new Set();
        const properties = {};

        // Determinar niveles EPP
        const eppLevels = determineEPPLevel(product.categories);

        if (eppLevels.level1 || eppLevels.level2 || eppLevels.level3) {
          eppProductsCount++;

          // NIVEL 1: EPP General
          if (eppLevels.level1) {
            tags.add('epp');
            tags.add('seguridad');
            tags.add('proteccion');
            properties.categoria_principal = 'EPP';
          }

          // NIVEL 2: Tipo de protección
          if (eppLevels.level2) {
            const level2Data = eppCategoryData[eppLevels.level2];
            if (level2Data) {
              // Agregar tags del nivel 2
              level2Data.tags.forEach(tag => tags.add(tag));

              // Agregar propiedades base del nivel 2
              Object.assign(properties, level2Data.baseProperties);

              console.log(`  📋 Nivel 2 detectado: ${eppLevels.level2} para ${product.name}`);
            }
          }

          // NIVEL 3: Subcategoría específica
          if (eppLevels.level3) {
            const level3Data = eppSubcategoryData[eppLevels.level3];
            if (level3Data) {
              // Agregar tags específicos del nivel 3
              level3Data.tags.forEach(tag => tags.add(tag));

              // Agregar/sobrescribir propiedades específicas del nivel 3
              Object.assign(properties, level3Data.properties);

              console.log(`  🎯 Nivel 3 detectado: ${eppLevels.level3} para ${product.name}`);
            }
          }
        }

        // Procesar categorías no-EPP (mantener compatibilidad)
        if (product.categories && Array.isArray(product.categories)) {
          product.categories.forEach(category => {
            // Solo procesar si no es una categoría EPP ya procesada
            if (category !== 'epp' && !eppCategoryData[category] && !eppSubcategoryData[category]) {
              const categoryTags = categoryTagsMap[category];
              if (categoryTags) {
                categoryTags.forEach(tag => tags.add(tag));
              }

              const categoryProps = categoryPropertiesMap[category];
              if (categoryProps) {
                Object.assign(properties, categoryProps);
              }
            }
          });
        }

        // Agregar tags basados en marca
        if (product.brand) {
          const brandTags = brandTagsMap[product.brand.toUpperCase()];
          if (brandTags) {
            brandTags.forEach(tag => tags.add(tag));
          }
        }

        // Agregar tags basados en industrias
        if (product.industries && Array.isArray(product.industries)) {
          product.industries.forEach(industry => {
            tags.add(industry.toLowerCase());
          });
        }

        // Agregar tags basados en el nombre del producto
        const nameWords = product.name.toLowerCase().split(' ');
        nameWords.forEach(word => {
          if (word.length > 3 && !['para', 'con', 'sin', 'tipo'].includes(word)) {
            tags.add(word);
          }
        });

        // Agregar propiedades específicas basadas en características
        if (product.caracteristicas) {
          const caracteristicas = product.caracteristicas.toLowerCase();
          
          // Detectar normativas
          const normativas = [];
          if (caracteristicas.includes('ansi')) normativas.push('ANSI');
          if (caracteristicas.includes('en 397')) normativas.push('EN 397');
          if (caracteristicas.includes('en 388')) normativas.push('EN 388');
          if (normativas.length > 0) {
            properties.normativa = normativas;
          }

          // Detectar materiales
          const materiales = [];
          if (caracteristicas.includes('abs')) materiales.push('ABS');
          if (caracteristicas.includes('policarbonato')) materiales.push('Policarbonato');
          if (caracteristicas.includes('nitrilo')) materiales.push('Nitrilo');
          if (caracteristicas.includes('cuero')) materiales.push('Cuero');
          if (materiales.length > 0) {
            properties.material = materiales;
          }

          // Detectar resistencias
          if (caracteristicas.includes('resistente al impacto')) {
            properties.resistencia_impacto = 'Alta';
          }
          if (caracteristicas.includes('resistente a químicos')) {
            properties.resistencia_quimica = true;
          }
        }

        // Agregar peso si está disponible en especificaciones
        if (product.especificaciones) {
          const pesoMatch = product.especificaciones.match(/(\d+)\s*g/i);
          if (pesoMatch) {
            properties.peso = parseInt(pesoMatch[1]);
          }
        }

        // Convertir Set a Array para tags
        const finalTags = Array.from(tags).slice(0, 10); // Limitar a 10 tags

        // Actualizar el producto
        const { error: updateError } = await supabase
          .from('products')
          .update({
            tags: finalTags,
            properties: properties
          })
          .eq('id', product.id);

        if (updateError) {
          throw updateError;
        }

        updatedCount++;
        console.log(`✅ Producto actualizado: ${product.name} (${finalTags.length} tags, ${Object.keys(properties).length} propiedades)`);

      } catch (error) {
        errorCount++;
        console.error(`❌ Error actualizando producto ${product.name}:`, error.message);
      }
    }

    console.log(`\n📊 Resumen:`);
    console.log(`   ✅ Productos actualizados: ${updatedCount}`);
    console.log(`   🛡️  Productos EPP procesados: ${eppProductsCount}`);
    console.log(`   ❌ Errores: ${errorCount}`);
    console.log(`   📦 Total procesados: ${products.length}`);

    // 3. Verificar productos EPP actualizados por nivel
    console.log('\n🔍 Verificando productos EPP por nivel...');

    // Productos con nivel 3 (más específicos)
    const { data: level3Products } = await supabase
      .from('products')
      .select('name, tags, properties, categories')
      .not('tags', 'is', null)
      .contains('properties', { subcategoria: 'Cascos Dieléctricos' })
      .limit(2);

    if (level3Products && level3Products.length > 0) {
      console.log('\n🎯 Productos Nivel 3 (Subcategorías específicas):');
      level3Products.forEach(product => {
        console.log(`\n📦 ${product.name}:`);
        console.log(`   📂 Categorías: ${product.categories?.join(', ') || 'Ninguna'}`);
        console.log(`   🏷️  Tags: ${product.tags?.slice(0, 5).join(', ') || 'Ninguno'}...`);
        console.log(`   🎯 Subcategoría: ${product.properties?.subcategoria || 'N/A'}`);
        console.log(`   📋 Normativa: ${product.properties?.normativa?.join(', ') || 'N/A'}`);
      });
    }

    // Productos con nivel 2 (tipos de protección)
    const { data: level2Products } = await supabase
      .from('products')
      .select('name, tags, properties, categories')
      .not('tags', 'is', null)
      .contains('properties', { tipo_proteccion: 'Craneana' })
      .limit(2);

    if (level2Products && level2Products.length > 0) {
      console.log('\n📋 Productos Nivel 2 (Tipos de protección):');
      level2Products.forEach(product => {
        console.log(`\n📦 ${product.name}:`);
        console.log(`   🛡️  Tipo: ${product.properties?.tipo_proteccion || 'N/A'}`);
        console.log(`   🏷️  Tags: ${product.tags?.slice(0, 5).join(', ') || 'Ninguno'}...`);
        console.log(`   🎯 Uso: ${product.properties?.uso_recomendado?.join(', ') || 'N/A'}`);
      });
    }

    // Estadísticas por tipo de protección
    console.log('\n📈 Estadísticas por tipo de protección EPP:');
    for (const [category, data] of Object.entries(eppCategoryData)) {
      const { data: categoryProducts } = await supabase
        .from('products')
        .select('id', { count: 'exact' })
        .contains('properties', { tipo_proteccion: data.baseProperties.tipo_proteccion });

      if (categoryProducts) {
        console.log(`   ${data.baseProperties.tipo_proteccion}: ${categoryProducts.length || 0} productos`);
      }
    }

  } catch (error) {
    console.error('❌ Error general:', error);
    process.exit(1);
  }
}

// Ejecutar el script
populateProductTagsAndProperties()
  .then(() => {
    console.log('\n🎉 ¡Población de tags y propiedades completada!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('💥 Error fatal:', error);
    process.exit(1);
  });
