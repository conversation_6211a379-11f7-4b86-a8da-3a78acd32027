// ============================================================================
// SERVER OPTIMIZADO - CR Work
// Arquitectura para 50+ usuarios simultáneos con seguridad reforzada
// ============================================================================

import dotenv from 'dotenv';
dotenv.config();

import express from 'express';
import cors from 'cors';
import compression from 'compression';
import { createClient } from '@supabase/supabase-js';
import path from 'path';
import { fileURLToPath } from 'url';

// Middlewares de seguridad y performance
import { applySecurity } from './middleware/security.js';
import { requestLogger } from './lib/logger.js';
import { authMiddleware } from './middleware/auth.js';

// Sistemas optimizados
import supabasePool from './lib/supabasePool.js';
import cacheSystem from './lib/cacheSystem.js';

// Rutas
import healthRoutes from './routes/health.js';
import logger from './lib/logger.js';

// Configuración
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// ============================================================================
// VALIDACIÓN DE ENTORNO
// ============================================================================

const requiredEnvVars = [
  'VITE_SUPABASE_URL',
  'VITE_SUPABASE_ANON_KEY'
];

const missingEnvVars = requiredEnvVars.filter(varName => !process.env[varName]);
if (missingEnvVars.length > 0) {
  logger.error('Missing required environment variables', { missingEnvVars });
  process.exit(1);
}

// ============================================================================
// CONFIGURACIÓN DE EXPRESS
// ============================================================================

const app = express();

// Configurar trust proxy para obtener IPs reales detrás de proxies
app.set('trust proxy', 1);

// ============================================================================
// MIDDLEWARES DE PERFORMANCE
// ============================================================================

// Compresión GZIP
app.use(compression({
  filter: (req, res) => {
    if (req.headers['x-no-compression']) {
      return false;
    }
    return compression.filter(req, res);
  },
  level: 6, // Nivel de compresión balanceado
  threshold: 1024 // Solo comprimir archivos > 1KB
}));

// ============================================================================
// MIDDLEWARES DE SEGURIDAD
// ============================================================================

// Aplicar todos los middlewares de seguridad
applySecurity(app);

// ============================================================================
// CORS CONFIGURADO
// ============================================================================

const corsOrigins = process.env.CORS_ORIGINS 
  ? process.env.CORS_ORIGINS.split(',').map(origin => origin.trim())
  : [
      'https://cr-seg-ind.pages.dev',
      'https://cr-seg-ind-production.up.railway.app',
      'https://cr-work.up.railway.app',
      'http://localhost:5173'
    ];

app.use(cors({
  origin: (origin, callback) => {
    // Permitir requests sin origin (mobile apps, Postman, etc.)
    if (!origin) return callback(null, true);
    
    if (corsOrigins.includes(origin)) {
      return callback(null, true);
    }
    
    logger.warn('CORS blocked request', { origin, allowedOrigins: corsOrigins });
    return callback(new Error('Not allowed by CORS'));
  },
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS', 'PATCH'],
  allowedHeaders: [
    'Content-Type', 
    'Authorization', 
    'X-Requested-With',
    'Accept',
    'Origin'
  ],
  credentials: true,
  maxAge: 86400 // 24 horas de cache para preflight
}));

// ============================================================================
// MIDDLEWARES BÁSICOS
// ============================================================================

// Body parsing con límites de seguridad
app.use(express.json({ 
  limit: '10mb',
  verify: (req, res, buf) => {
    // Verificar que el JSON sea válido
    try {
      JSON.parse(buf);
    } catch (e) {
      logger.warn('Invalid JSON received', { ip: req.ip, path: req.path });
      throw new Error('Invalid JSON');
    }
  }
}));

app.use(express.urlencoded({ 
  extended: true, 
  limit: '10mb' 
}));

// Request logging
app.use(requestLogger);

// ============================================================================
// ARCHIVOS ESTÁTICOS CON CACHÉ OPTIMIZADO
// ============================================================================

app.use(express.static('public', {
  maxAge: '1d',
  etag: true,
  lastModified: true
}));

app.use('/images', express.static('public/images', {
  maxAge: '7d', // Imágenes pueden cachearse más tiempo
  etag: true,
  lastModified: true,
  setHeaders: (res, path) => {
    res.setHeader('Cache-Control', 'public, max-age=604800'); // 7 días
    res.setHeader('Access-Control-Allow-Origin', '*');
    
    // Optimizaciones adicionales para imágenes
    if (path.endsWith('.webp')) {
      res.setHeader('Content-Type', 'image/webp');
    } else if (path.endsWith('.avif')) {
      res.setHeader('Content-Type', 'image/avif');
    }
  }
}));

// ============================================================================
// RUTAS PÚBLICAS (SIN AUTENTICACIÓN)
// ============================================================================

// Health checks
app.use('/', healthRoutes);

// Endpoint de métricas (solo en desarrollo)
if (process.env.NODE_ENV !== 'production') {
  app.get('/metrics', async (req, res) => {
    try {
      const poolMetrics = supabasePool.getMetrics();
      const cacheMetrics = cacheSystem.getMetrics();
      const cacheHealth = await cacheSystem.healthCheck();
      
      res.json({
        timestamp: new Date().toISOString(),
        environment: process.env.NODE_ENV,
        uptime: process.uptime(),
        memory: process.memoryUsage(),
        supabasePool: poolMetrics,
        cache: {
          ...cacheMetrics,
          health: cacheHealth
        }
      });
    } catch (error) {
      logger.error('Metrics endpoint error', { error: error.message });
      res.status(500).json({ error: 'Failed to get metrics' });
    }
  });
}

// ============================================================================
// RUTAS PROTEGIDAS (CON AUTENTICACIÓN)
// ============================================================================

// Middleware de autenticación para rutas /api
app.use('/api', authMiddleware, (req, res, next) => {
  logger.debug('Authenticated request', { 
    user: req.user?.id,
    path: req.path,
    method: req.method
  });
  next();
});

// ============================================================================
// RUTAS DE API CON CACHÉ
// ============================================================================

// Ejemplo de ruta con caché integrado
app.get('/api/products', async (req, res) => {
  try {
    const cacheKey = 'all_products';
    const filters = req.query;
    
    // Intentar obtener del caché
    const cachedProducts = await cacheSystem.get('products', cacheKey, filters);
    if (cachedProducts) {
      res.setHeader('X-Cache', 'HIT');
      return res.json(cachedProducts);
    }
    
    // Obtener conexión del pool
    const connection = await supabasePool.getConnection();
    
    try {
      // Consulta a la base de datos
      const { data: products, error } = await connection.client
        .from('products')
        .select('*')
        .limit(100);
      
      if (error) throw error;
      
      // Guardar en caché
      await cacheSystem.set('products', cacheKey, products, 600, filters); // 10 minutos
      
      res.setHeader('X-Cache', 'MISS');
      res.json(products);
      
    } finally {
      // Liberar conexión
      supabasePool.releaseConnection(connection);
    }
    
  } catch (error) {
    logger.error('Products API error', { error: error.message });
    res.status(500).json({ 
      error: process.env.NODE_ENV === 'production' 
        ? 'Internal server error' 
        : error.message 
    });
  }
});

// ============================================================================
// MANEJO DE ERRORES GLOBAL
// ============================================================================

// 404 Handler
app.use('*', (req, res) => {
  logger.warn('404 Not Found', { 
    path: req.originalUrl, 
    method: req.method,
    ip: req.ip 
  });
  
  res.status(404).json({
    error: 'Endpoint not found',
    path: req.originalUrl
  });
});

// Error Handler Global
app.use((err, req, res, next) => {
  logger.error('Unhandled error', { 
    error: err.message,
    stack: process.env.NODE_ENV !== 'production' ? err.stack : undefined,
    path: req.path,
    method: req.method,
    ip: req.ip
  });
  
  // No enviar stack trace en producción
  const errorResponse = {
    error: process.env.NODE_ENV === 'production' 
      ? 'Internal server error' 
      : err.message
  };
  
  res.status(err.status || 500).json(errorResponse);
});

// ============================================================================
// INICIALIZACIÓN DEL SERVIDOR
// ============================================================================

const PORT = process.env.PORT || 3010;

const server = app.listen(PORT, '0.0.0.0', () => {
  logger.info('Server started successfully', {
    port: PORT,
    environment: process.env.NODE_ENV,
    corsOrigins: corsOrigins.length,
    features: {
      supabasePool: true,
      cacheSystem: true,
      security: true,
      compression: true
    }
  });
});

// ============================================================================
// GRACEFUL SHUTDOWN
// ============================================================================

const gracefulShutdown = async (signal) => {
  logger.info(`Received ${signal}, starting graceful shutdown...`);
  
  // Cerrar servidor HTTP
  server.close(async () => {
    logger.info('HTTP server closed');
    
    try {
      // Cerrar pool de conexiones
      await supabasePool.shutdown();
      
      // Cerrar sistema de caché
      await cacheSystem.shutdown();
      
      logger.info('Graceful shutdown completed');
      process.exit(0);
    } catch (error) {
      logger.error('Error during shutdown', { error: error.message });
      process.exit(1);
    }
  });
  
  // Forzar cierre después de 30 segundos
  setTimeout(() => {
    logger.error('Forced shutdown after timeout');
    process.exit(1);
  }, 30000);
};

// Manejar señales de cierre
process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
process.on('SIGINT', () => gracefulShutdown('SIGINT'));

// Manejar errores no capturados
process.on('uncaughtException', (error) => {
  logger.error('Uncaught Exception', { error: error.message, stack: error.stack });
  gracefulShutdown('uncaughtException');
});

process.on('unhandledRejection', (reason, promise) => {
  logger.error('Unhandled Rejection', { reason, promise });
  gracefulShutdown('unhandledRejection');
});

export default app;
