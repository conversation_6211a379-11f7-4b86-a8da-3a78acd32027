# Estructura EPP de 3 Niveles - Sistema de Categorización

## 📋 Resumen del Sistema

He implementado un sistema de categorización EPP de **3 niveles jerárquicos** que permite una clasificación granular y filtrado específico de productos de Equipos de Protección Personal.

## 🏗️ Estructura de los 3 Niveles

### **NIVEL 1: EPP (Categoría Principal)**
```
epp
```
- **Propósito**: Identificar que el producto pertenece a Equipos de Protección Personal
- **Tags comunes**: `epp`, `seguridad`, `proteccion`
- **Propiedades**: `categoria_principal: "EPP"`

### **NIVEL 2: Tipos de Protección**
```
epp → [tipo_proteccion]
```

| Tipo | ID | Descripción | Tags Específicos |
|------|----|-----------|--------------------|
| **Craneana** | `craneana` | Protección de cabeza | `seguridad`, `cabeza`, `impacto`, `casco` |
| **Ocular** | `ocular` | Protección de ojos | `vision`, `proteccion`, `ojos`, `transparente` |
| **Facial** | `facial` | Protección de rostro | `rostro`, `cara`, `salpicaduras`, `protector` |
| **Respiratoria** | `respiratoria` | Protección respiratoria | `aire`, `respiracion`, `filtros`, `mascarilla` |
| **Indumentaria** | `indumentaria` | Ropa de protección | `ropa`, `trabajo`, `comodidad`, `uniforme` |
| **Manos** | `manos` | Protección de manos | `guantes`, `manos`, `agarre`, `destreza` |
| **Calzado** | `calzado` | Protección de pies | `pies`, `calzado`, `antideslizante`, `seguridad` |
| **Auditiva** | `auditiva` | Protección auditiva | `ruido`, `oidos`, `auditiva`, `confort` |
| **Contra Fuego** | `contra-fuego` | Protección ignífuga | `ignifugo`, `fuego`, `calor`, `bombero` |
| **Altura** | `altura` | Protección para altura | `altura`, `arnes`, `caidas`, `rescate` |

### **NIVEL 3: Subcategorías Específicas**
```
epp → [tipo_proteccion] → [subcategoria_especifica]
```

#### 🪖 **PROTECCIÓN CRANEANA**
- **`dielectricos`**: Cascos dieléctricos para trabajos eléctricos
- **`altura`**: Cascos especializados para trabajos en altura
- **`gorra-casquete`**: Gorras con casquete para bajo riesgo
- **`mentoneras`**: Accesorios de sujeción para cascos

#### 👁️ **PROTECCIÓN OCULAR**
- **`anteojos`**: Anteojos de seguridad básicos
- **`antiparras`**: Antiparras herméticas para químicos

#### 🛡️ **PROTECCIÓN FACIAL**
- **`plano`**: Protectores faciales planos básicos
- **`burbuja`**: Protectores envolventes panorámicos
- **`forestal`**: Protección con malla para uso forestal
- **`deflagatoria`**: Protección contra arco eléctrico
- **`soldador`**: Caretas especializadas para soldadura

#### 😷 **PROTECCIÓN RESPIRATORIA**
- **`mascarillas-descartables`**: Mascarillas N95 y similares
- **`mascaras`**: Máscaras reutilizables con filtros
- **`equipos-autonomos`**: Equipos de aire autónomos

#### 🧤 **PROTECCIÓN DE MANOS**
- **`anticorte`**: Guantes resistentes al corte
- **`temperatura`**: Guantes para altas/bajas temperaturas
- **`impacto`**: Guantes anti-impacto y vibración
- **`nitrilicos`**: Guantes nitrílicos para químicos

#### 👢 **CALZADO DE SEGURIDAD**
- **`zapatos`**: Zapatos de seguridad básicos
- **`botinas`**: Botinas de media caña
- **`zapatillas`**: Zapatillas deportivas de seguridad

## 🎯 Ejemplos de Categorización

### **Ejemplo 1: Casco Dieléctrico**
```json
{
  "categories": ["epp", "craneana", "dielectricos"],
  "tags": ["epp", "seguridad", "proteccion", "cabeza", "casco", "dielectrico", "electricidad"],
  "properties": {
    "categoria_principal": "EPP",
    "tipo_proteccion": "Craneana",
    "subcategoria": "Cascos Dieléctricos",
    "normativa": ["ANSI Z89.1 Clase E", "IEC 60903"],
    "voltaje_max": "20000V",
    "resistencia_electrica": "Clase E"
  }
}
```

### **Ejemplo 2: Guantes Anticorte**
```json
{
  "categories": ["epp", "manos", "anticorte"],
  "tags": ["epp", "seguridad", "guantes", "anticorte", "kevlar", "resistente"],
  "properties": {
    "categoria_principal": "EPP",
    "tipo_proteccion": "Manos",
    "subcategoria": "Guantes Anticorte",
    "normativa": ["EN 388"],
    "nivel_corte": ["3", "4", "5"],
    "material": ["Kevlar", "HPPE"]
  }
}
```

### **Ejemplo 3: Solo Nivel 2 (Sin subcategoría)**
```json
{
  "categories": ["epp", "auditiva"],
  "tags": ["epp", "seguridad", "ruido", "oidos", "confort"],
  "properties": {
    "categoria_principal": "EPP",
    "tipo_proteccion": "Auditiva",
    "normativa": ["ANSI S3.19", "EN 352"],
    "reduccion_ruido": "25-35 dB"
  }
}
```

## 🔧 Scripts Implementados

### **1. `populateProductTagsAndProperties.js`**
- **Función**: Asigna automáticamente tags y propiedades basados en la estructura de 3 niveles
- **Lógica**: Detecta el nivel EPP y aplica propiedades correspondientes
- **Uso**: `node scripts/populateProductTagsAndProperties.js`

### **2. `generateEPPSampleData.js`**
- **Función**: Genera productos de ejemplo que demuestran la estructura
- **Incluye**: 8 productos de ejemplo con diferentes niveles de categorización
- **Uso**: `node scripts/generateEPPSampleData.js`

## 🎨 Beneficios del Sistema de 3 Niveles

### **✅ Filtrado Granular**
- **Nivel 1**: Filtrar solo productos EPP
- **Nivel 2**: Filtrar por tipo de protección (ej: solo cascos)
- **Nivel 3**: Filtrar por aplicación específica (ej: solo cascos dieléctricos)

### **✅ Navegación Intuitiva**
```
EPP (1,250 productos)
├── Protección Craneana (180 productos)
│   ├── Cascos Dieléctricos (25 productos)
│   ├── Cascos de Altura (18 productos)
│   └── Gorras con Casquete (12 productos)
├── Protección Ocular (220 productos)
│   ├── Anteojos (150 productos)
│   └── Antiparras (70 productos)
└── ...
```

### **✅ Propiedades Específicas**
- **Nivel 2**: Propiedades generales del tipo (normativas base, materiales comunes)
- **Nivel 3**: Propiedades específicas (voltaje máximo, nivel de corte, etc.)

### **✅ SEO y Búsqueda**
- **Tags jerárquicos** mejoran la búsqueda
- **Propiedades estructuradas** permiten filtros facetados
- **Compatibilidad** con motores de búsqueda

## 🚀 Implementación en la UI

### **Filtros Jerárquicos**
```tsx
// Ejemplo de filtros en cascada
<FilterSection title="EPP">
  <FilterLevel2 title="Tipo de Protección">
    <FilterOption value="craneana">Protección Craneana (180)</FilterOption>
    <FilterLevel3 parent="craneana">
      <FilterOption value="dielectricos">Dieléctricos (25)</FilterOption>
      <FilterOption value="altura">Altura (18)</FilterOption>
    </FilterLevel3>
  </FilterLevel2>
</FilterSection>
```

### **Breadcrumbs Dinámicos**
```
Inicio > EPP > Protección Craneana > Cascos Dieléctricos
```

### **Facetas Inteligentes**
- **Normativas** relevantes al nivel seleccionado
- **Materiales** específicos del tipo de protección
- **Aplicaciones** filtradas por subcategoría

## 📊 Métricas y Analytics

### **Tracking por Nivel**
- **Nivel 1**: Interés general en EPP
- **Nivel 2**: Preferencias por tipo de protección
- **Nivel 3**: Necesidades específicas de aplicación

### **Optimización de Inventario**
- **Identificar** subcategorías más demandadas
- **Balancear** stock por nivel de especificidad
- **Predecir** tendencias de compra

## 🔄 Migración y Compatibilidad

### **Productos Existentes**
- **Automática**: El script detecta categorías existentes
- **Gradual**: Se pueden migrar productos por lotes
- **Reversible**: Mantiene compatibilidad con estructura anterior

### **APIs y Integraciones**
- **Backward compatible**: APIs existentes siguen funcionando
- **Extended**: Nuevos endpoints para filtrado avanzado
- **Flexible**: Soporte para consultas de cualquier nivel

Este sistema de 3 niveles proporciona la flexibilidad necesaria para manejar la complejidad de los productos EPP mientras mantiene una experiencia de usuario intuitiva y un sistema de gestión eficiente.
