-- Migración para agregar sistema de tags y propiedades a productos
-- Esta migración extiende la funcionalidad sin romper la estructura existente

-- 1. Agregar campos de tags y propiedades a la tabla products
ALTER TABLE public.products 
ADD COLUMN IF NOT EXISTS tags text[] DEFAULT '{}',
ADD COLUMN IF NOT EXISTS properties jsonb DEFAULT '{}',
ADD COLUMN IF NOT EXISTS search_vector tsvector;

-- 2. <PERSON>rear índices optimizados para el nuevo sistema
CREATE INDEX IF NOT EXISTS idx_products_tags ON public.products USING GIN (tags);
CREATE INDEX IF NOT EXISTS idx_products_properties ON public.products USING GIN (properties);
CREATE INDEX IF NOT EXISTS idx_products_search_vector ON public.products USING GIN (search_vector);

-- 3. Crear función para actualizar el vector de búsqueda
CREATE OR REPLACE FUNCTION update_product_search_vector()
RETURNS TRIGGER AS $$
BEGIN
    NEW.search_vector := 
        setweight(to_tsvector('spanish', COALESCE(NEW.name, '')), 'A') ||
        setweight(to_tsvector('spanish', COALESCE(NEW.description, '')), 'B') ||
        setweight(to_tsvector('spanish', COALESCE(NEW.brand, '')), 'C') ||
        setweight(to_tsvector('spanish', COALESCE(NEW.caracteristicas, '')), 'D') ||
        setweight(to_tsvector('spanish', COALESCE(NEW.especificaciones, '')), 'D') ||
        setweight(to_tsvector('spanish', array_to_string(NEW.tags, ' ')), 'B') ||
        setweight(to_tsvector('spanish', array_to_string(NEW.categories, ' ')), 'C') ||
        setweight(to_tsvector('spanish', array_to_string(NEW.industries, ' ')), 'C');
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 4. Crear trigger para actualizar automáticamente el vector de búsqueda
DROP TRIGGER IF EXISTS trigger_update_product_search_vector ON public.products;
CREATE TRIGGER trigger_update_product_search_vector
    BEFORE INSERT OR UPDATE ON public.products
    FOR EACH ROW
    EXECUTE FUNCTION update_product_search_vector();

-- 5. Crear tabla para definir tipos de propiedades (metadatos)
CREATE TABLE IF NOT EXISTS public.product_property_types (
    id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
    name text NOT NULL UNIQUE,
    display_name text NOT NULL,
    data_type text NOT NULL CHECK (data_type IN ('text', 'number', 'boolean', 'select', 'multiselect')),
    options jsonb DEFAULT '[]', -- Para tipos select/multiselect
    category_ids text[] DEFAULT '{}', -- Categorías donde aplica esta propiedad
    is_filterable boolean DEFAULT true,
    is_searchable boolean DEFAULT true,
    sort_order integer DEFAULT 0,
    created_at timestamp with time zone DEFAULT now()
);

-- 6. Insertar tipos de propiedades comunes para EPP
INSERT INTO public.product_property_types (name, display_name, data_type, options, category_ids, is_filterable, is_searchable) VALUES
('normativa', 'Normativa', 'multiselect', '["ANSI Z89.1", "EN 397", "EN 812", "ANSI Z87.1", "EN 166", "EN 1731", "NFPA 70E", "EN 388", "EN 407", "EN 511", "EN 374", "ANSI Z359.11", "ANSI Z359.13", "ANSI Z359.14"]', '{"craneana", "ocular", "facial", "manos", "altura"}', true, true),
('material', 'Material', 'multiselect', '["ABS", "Polietileno", "Policarbonato", "Nylon", "Cuero", "Nitrilo", "Látex", "PVC", "Kevlar", "Nomex", "Algodón", "Poliéster"]', '{}', true, true),
('talla', 'Talla', 'multiselect', '["XS", "S", "M", "L", "XL", "XXL", "XXXL", "Única"]', '{"indumentaria", "manos", "calzado"}', true, false),
('color', 'Color', 'multiselect', '["Blanco", "Amarillo", "Naranja", "Rojo", "Azul", "Verde", "Negro", "Gris", "Transparente"]', '{}', true, false),
('resistencia_impacto', 'Resistencia al Impacto', 'select', '["Baja", "Media", "Alta", "Muy Alta"]', '{"craneana", "ocular", "facial"}', true, true),
('resistencia_quimica', 'Resistencia Química', 'boolean', '[]', '{"manos", "indumentaria", "respiratoria"}', true, true),
('resistencia_corte', 'Nivel de Resistencia al Corte', 'select', '["Nivel 1", "Nivel 2", "Nivel 3", "Nivel 4", "Nivel 5"]', '{"manos"}', true, true),
('certificacion', 'Certificación', 'multiselect', '["CE", "ANSI", "OSHA", "NIOSH", "ISO", "IRAM"]', '{}', true, true),
('uso_recomendado', 'Uso Recomendado', 'multiselect', '["Construcción", "Industria", "Laboratorio", "Soldadura", "Química", "Alimentaria", "Minería", "Petróleo", "Electricidad"]', '{}', true, true),
('peso', 'Peso (gramos)', 'number', '[]', '{}', true, false);

-- 7. Crear índices para la tabla de tipos de propiedades
CREATE INDEX IF NOT EXISTS idx_property_types_category_ids ON public.product_property_types USING GIN (category_ids);
CREATE INDEX IF NOT EXISTS idx_property_types_filterable ON public.product_property_types (is_filterable);

-- 8. Habilitar RLS para la nueva tabla
ALTER TABLE public.product_property_types ENABLE ROW LEVEL SECURITY;

-- 9. Crear políticas para la tabla de tipos de propiedades
CREATE POLICY "Property types are viewable by everyone" 
ON public.product_property_types FOR SELECT 
TO public 
USING (true);

CREATE POLICY "Authenticated users can modify property types" 
ON public.product_property_types FOR ALL 
TO authenticated 
USING (true);

-- 10. Actualizar el vector de búsqueda para productos existentes
UPDATE public.products SET updated_at = now() WHERE id IS NOT NULL;

-- 11. Crear función para obtener propiedades filtrables por categoría
CREATE OR REPLACE FUNCTION get_filterable_properties_by_categories(category_ids text[])
RETURNS TABLE (
    property_name text,
    display_name text,
    data_type text,
    options jsonb
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        pt.name,
        pt.display_name,
        pt.data_type,
        pt.options
    FROM public.product_property_types pt
    WHERE pt.is_filterable = true
    AND (
        pt.category_ids = '{}' OR -- Propiedades globales
        pt.category_ids && category_ids -- Propiedades específicas de categoría
    )
    ORDER BY pt.sort_order, pt.display_name;
END;
$$ LANGUAGE plpgsql;

-- 12. Crear función para búsqueda avanzada de productos
CREATE OR REPLACE FUNCTION search_products_advanced(
    search_term text DEFAULT '',
    category_filters text[] DEFAULT '{}',
    industry_filters text[] DEFAULT '{}',
    tag_filters text[] DEFAULT '{}',
    property_filters jsonb DEFAULT '{}'
)
RETURNS TABLE (
    id uuid,
    name text,
    description text,
    price numeric,
    categories text[],
    industries text[],
    tags text[],
    properties jsonb,
    brand text,
    stock integer,
    image_url text,
    rank real
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        p.id,
        p.name,
        p.description,
        p.price,
        p.categories,
        p.industries,
        p.tags,
        p.properties,
        p.brand,
        p.stock,
        p.image_url,
        CASE 
            WHEN search_term = '' THEN 0
            ELSE ts_rank(p.search_vector, plainto_tsquery('spanish', search_term))
        END as rank
    FROM public.products p
    WHERE 
        (search_term = '' OR p.search_vector @@ plainto_tsquery('spanish', search_term))
        AND (category_filters = '{}' OR p.categories && category_filters)
        AND (industry_filters = '{}' OR p.industries && industry_filters)
        AND (tag_filters = '{}' OR p.tags && tag_filters)
        AND (
            property_filters = '{}' OR
            (p.properties @> property_filters)
        )
    ORDER BY 
        CASE WHEN search_term = '' THEN p.name ELSE rank END DESC,
        p.name;
END;
$$ LANGUAGE plpgsql;
