import { useEffect, useRef, useState } from 'react';
import { getAllEPPCategoryImageUrls, preloadImagesWithPromises } from '../utils/imageUtils';

interface UseFirstScrollPreloadOptions {
  threshold?: number; // Píxeles de scroll necesarios para activar la precarga
  enabled?: boolean; // Si la precarga está habilitada
}

interface UseFirstScrollPreloadReturn {
  hasScrolled: boolean;
  isPreloading: boolean;
  preloadComplete: boolean;
  preloadError: boolean;
}

/**
 * Hook personalizado que detecta el primer scroll del usuario y precarga las imágenes EPP
 * @param options Opciones de configuración
 * @returns Estado de la precarga
 */
export const useFirstScrollPreload = (
  options: UseFirstScrollPreloadOptions = {}
): UseFirstScrollPreloadReturn => {
  const { threshold = 50, enabled = true } = options;
  
  const [hasScrolled, setHasScrolled] = useState(false);
  const [isPreloading, setIsPreloading] = useState(false);
  const [preloadComplete, setPreloadComplete] = useState(false);
  const [preloadError, setPreloadError] = useState(false);
  
  const preloadTriggered = useRef(false);
  const scrollListenerAdded = useRef(false);

  useEffect(() => {
    if (!enabled || preloadTriggered.current || scrollListenerAdded.current) {
      return;
    }

    const handleScroll = async () => {
      const scrollY = window.scrollY || window.pageYOffset;
      
      if (scrollY > threshold && !preloadTriggered.current) {
        setHasScrolled(true);
        preloadTriggered.current = true;
        
        // Remover el listener inmediatamente para evitar múltiples ejecuciones
        window.removeEventListener('scroll', handleScroll);
        scrollListenerAdded.current = false;
        
        console.log('🚀 Primer scroll detectado, iniciando precarga de imágenes EPP...');
        
        try {
          setIsPreloading(true);
          setPreloadError(false);
          
          // Obtener todas las URLs de imágenes EPP
          const eppImageUrls = getAllEPPCategoryImageUrls();
          
          if (eppImageUrls.length > 0) {
            console.log(`📸 Precargando ${eppImageUrls.length} imágenes de categorías EPP...`);
            
            // Precargar las imágenes
            await preloadImagesWithPromises(eppImageUrls);
            
            setPreloadComplete(true);
            console.log('✅ Precarga de imágenes EPP completada exitosamente');
          } else {
            console.warn('⚠️ No se encontraron URLs de imágenes EPP para precargar');
            setPreloadComplete(true);
          }
        } catch (error) {
          console.error('❌ Error durante la precarga de imágenes EPP:', error);
          setPreloadError(true);
        } finally {
          setIsPreloading(false);
        }
      }
    };

    // Agregar el listener de scroll
    window.addEventListener('scroll', handleScroll, { passive: true });
    scrollListenerAdded.current = true;

    // Cleanup function
    return () => {
      if (scrollListenerAdded.current) {
        window.removeEventListener('scroll', handleScroll);
        scrollListenerAdded.current = false;
      }
    };
  }, [threshold, enabled]);

  return {
    hasScrolled,
    isPreloading,
    preloadComplete,
    preloadError
  };
};
