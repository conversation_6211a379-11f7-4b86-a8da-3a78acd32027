import { createClient } from '@supabase/supabase-js';
import logger from './logger.js';

// ============================================================================
// SUPABASE CONNECTION POOL - Optimización para 50+ usuarios simultáneos
// ============================================================================

class SupabasePool {
  constructor() {
    this.pool = [];
    this.activeConnections = new Set();
    this.maxConnections = parseInt(process.env.SUPABASE_MAX_CONNECTIONS) || 15;
    this.minConnections = parseInt(process.env.SUPABASE_MIN_CONNECTIONS) || 3;
    this.currentConnections = 0;
    this.waitingQueue = [];
    this.connectionTimeout = 30000; // 30 segundos
    this.idleTimeout = 300000; // 5 minutos
    this.healthCheckInterval = 60000; // 1 minuto
    
    // Métricas
    this.metrics = {
      totalRequests: 0,
      activeRequests: 0,
      poolHits: 0,
      poolMisses: 0,
      connectionErrors: 0,
      avgResponseTime: 0
    };
    
    this.initialize();
  }

  async initialize() {
    try {
      // Crear conexiones mínimas al inicio
      for (let i = 0; i < this.minConnections; i++) {
        const connection = await this.createConnection();
        this.pool.push({
          client: connection,
          created: Date.now(),
          lastUsed: Date.now(),
          isHealthy: true,
          id: `conn_${i}`
        });
      }
      
      // Iniciar health check periódico
      this.startHealthCheck();
      
      logger.info('Supabase connection pool initialized', {
        minConnections: this.minConnections,
        maxConnections: this.maxConnections,
        initialPool: this.pool.length
      });
      
    } catch (error) {
      logger.error('Failed to initialize Supabase pool', { error: error.message });
      throw error;
    }
  }

  async createConnection() {
    try {
      const client = createClient(
        process.env.VITE_SUPABASE_URL,
        process.env.VITE_SUPABASE_ANON_KEY,
        {
          auth: { 
            persistSession: false,
            autoRefreshToken: false
          },
          db: { 
            schema: 'public'
          },
          global: { 
            headers: { 
              'x-connection-pool': 'true',
              'x-pool-id': `pool_${Date.now()}`
            }
          },
          realtime: {
            params: {
              eventsPerSecond: 10
            }
          }
        }
      );

      // Verificar que la conexión funciona
      await this.testConnection(client);
      
      this.currentConnections++;
      return client;
      
    } catch (error) {
      this.metrics.connectionErrors++;
      logger.error('Failed to create Supabase connection', { error: error.message });
      throw error;
    }
  }

  async testConnection(client) {
    try {
      const { data, error } = await client
        .from('products')
        .select('count')
        .limit(1)
        .single();
      
      if (error && error.code !== 'PGRST116') { // PGRST116 = no rows, but connection works
        throw error;
      }
      
      return true;
    } catch (error) {
      throw new Error(`Connection test failed: ${error.message}`);
    }
  }

  async getConnection() {
    const startTime = Date.now();
    this.metrics.totalRequests++;
    this.metrics.activeRequests++;

    try {
      // Intentar obtener conexión del pool
      if (this.pool.length > 0) {
        const connectionWrapper = this.pool.pop();
        
        // Verificar si la conexión sigue siendo saludable
        if (this.isConnectionHealthy(connectionWrapper)) {
          connectionWrapper.lastUsed = Date.now();
          this.activeConnections.add(connectionWrapper);
          this.metrics.poolHits++;
          
          logger.debug('Connection retrieved from pool', {
            poolSize: this.pool.length,
            activeConnections: this.activeConnections.size,
            connectionId: connectionWrapper.id
          });
          
          return connectionWrapper;
        } else {
          // Conexión no saludable, crear una nueva
          await this.destroyConnection(connectionWrapper);
        }
      }

      // Si no hay conexiones disponibles y no hemos alcanzado el máximo
      if (this.currentConnections < this.maxConnections) {
        const client = await this.createConnection();
        const connectionWrapper = {
          client,
          created: Date.now(),
          lastUsed: Date.now(),
          isHealthy: true,
          id: `conn_${Date.now()}`
        };
        
        this.activeConnections.add(connectionWrapper);
        this.metrics.poolMisses++;
        
        logger.debug('New connection created', {
          totalConnections: this.currentConnections,
          maxConnections: this.maxConnections,
          connectionId: connectionWrapper.id
        });
        
        return connectionWrapper;
      }

      // Pool lleno, esperar por conexión disponible
      return await this.waitForConnection();
      
    } catch (error) {
      this.metrics.connectionErrors++;
      logger.error('Failed to get connection', { error: error.message });
      throw error;
    } finally {
      this.metrics.activeRequests--;
      const responseTime = Date.now() - startTime;
      this.updateAvgResponseTime(responseTime);
    }
  }

  async waitForConnection() {
    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error('Connection timeout: No available connections'));
      }, this.connectionTimeout);

      const checkForConnection = () => {
        if (this.pool.length > 0) {
          clearTimeout(timeout);
          this.getConnection().then(resolve).catch(reject);
        } else {
          setTimeout(checkForConnection, 100); // Check every 100ms
        }
      };

      this.waitingQueue.push({ resolve, reject, timeout });
      checkForConnection();
    });
  }

  releaseConnection(connectionWrapper) {
    try {
      if (!connectionWrapper || !connectionWrapper.client) {
        logger.warn('Attempted to release invalid connection');
        return;
      }

      // Remover de conexiones activas
      this.activeConnections.delete(connectionWrapper);
      
      // Verificar si la conexión sigue siendo saludable
      if (this.isConnectionHealthy(connectionWrapper)) {
        connectionWrapper.lastUsed = Date.now();
        this.pool.push(connectionWrapper);
        
        logger.debug('Connection returned to pool', {
          poolSize: this.pool.length,
          activeConnections: this.activeConnections.size,
          connectionId: connectionWrapper.id
        });
        
        // Procesar cola de espera
        this.processWaitingQueue();
      } else {
        // Conexión no saludable, destruir
        this.destroyConnection(connectionWrapper);
      }
      
    } catch (error) {
      logger.error('Error releasing connection', { error: error.message });
      this.destroyConnection(connectionWrapper);
    }
  }

  isConnectionHealthy(connectionWrapper) {
    const now = Date.now();
    const age = now - connectionWrapper.created;
    const idle = now - connectionWrapper.lastUsed;
    
    // Conexión muy vieja o inactiva por mucho tiempo
    if (age > 3600000 || idle > this.idleTimeout) { // 1 hora o idle timeout
      return false;
    }
    
    return connectionWrapper.isHealthy;
  }

  async destroyConnection(connectionWrapper) {
    try {
      if (connectionWrapper && connectionWrapper.client) {
        // Supabase no tiene método explícito de close, pero podemos limpiar
        connectionWrapper.client = null;
        connectionWrapper.isHealthy = false;
      }
      
      this.activeConnections.delete(connectionWrapper);
      this.currentConnections = Math.max(0, this.currentConnections - 1);
      
      logger.debug('Connection destroyed', {
        remainingConnections: this.currentConnections,
        poolSize: this.pool.length
      });
      
    } catch (error) {
      logger.error('Error destroying connection', { error: error.message });
    }
  }

  processWaitingQueue() {
    if (this.waitingQueue.length > 0 && this.pool.length > 0) {
      const { resolve, reject, timeout } = this.waitingQueue.shift();
      clearTimeout(timeout);
      this.getConnection().then(resolve).catch(reject);
    }
  }

  startHealthCheck() {
    setInterval(async () => {
      await this.performHealthCheck();
    }, this.healthCheckInterval);
  }

  async performHealthCheck() {
    try {
      // Verificar conexiones en el pool
      const unhealthyConnections = [];
      
      for (const connectionWrapper of this.pool) {
        try {
          await this.testConnection(connectionWrapper.client);
          connectionWrapper.isHealthy = true;
        } catch (error) {
          connectionWrapper.isHealthy = false;
          unhealthyConnections.push(connectionWrapper);
        }
      }
      
      // Remover conexiones no saludables
      for (const unhealthy of unhealthyConnections) {
        const index = this.pool.indexOf(unhealthy);
        if (index > -1) {
          this.pool.splice(index, 1);
          await this.destroyConnection(unhealthy);
        }
      }
      
      // Asegurar conexiones mínimas
      while (this.pool.length + this.activeConnections.size < this.minConnections) {
        try {
          const client = await this.createConnection();
          this.pool.push({
            client,
            created: Date.now(),
            lastUsed: Date.now(),
            isHealthy: true,
            id: `conn_health_${Date.now()}`
          });
        } catch (error) {
          logger.error('Failed to create connection during health check', { error: error.message });
          break;
        }
      }
      
      logger.debug('Health check completed', {
        poolSize: this.pool.length,
        activeConnections: this.activeConnections.size,
        totalConnections: this.currentConnections,
        unhealthyRemoved: unhealthyConnections.length
      });
      
    } catch (error) {
      logger.error('Health check failed', { error: error.message });
    }
  }

  updateAvgResponseTime(responseTime) {
    this.metrics.avgResponseTime = 
      (this.metrics.avgResponseTime * 0.9) + (responseTime * 0.1);
  }

  getMetrics() {
    return {
      ...this.metrics,
      poolSize: this.pool.length,
      activeConnections: this.activeConnections.size,
      totalConnections: this.currentConnections,
      maxConnections: this.maxConnections,
      waitingQueue: this.waitingQueue.length
    };
  }

  async shutdown() {
    logger.info('Shutting down Supabase connection pool...');
    
    // Destruir todas las conexiones
    const allConnections = [...this.pool, ...this.activeConnections];
    for (const connectionWrapper of allConnections) {
      await this.destroyConnection(connectionWrapper);
    }
    
    this.pool = [];
    this.activeConnections.clear();
    this.currentConnections = 0;
    
    logger.info('Supabase connection pool shutdown complete');
  }
}

// Singleton instance
export const supabasePool = new SupabasePool();

// Graceful shutdown
process.on('SIGTERM', async () => {
  await supabasePool.shutdown();
});

process.on('SIGINT', async () => {
  await supabasePool.shutdown();
});

export default supabasePool;
